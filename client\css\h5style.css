html,
body,
#viewDiv {
  padding: 0;
  margin: 0;
}

.esri-ui-top-left {
  top: 36px !important;
}

.esri-widget--button {
  width: 24px;
  height: 24px;
}

/*去掉地图黑边框*/
.esri-view-surface--inset-outline:focus::after {
  outline: none !important;
}

.layui-form-checked[lay-skin='primary'] i {
  border-color: #529fe1 !important;
  background-color: #529fe1;
  color: #fff;
}

.layui-icon-color {
  color: #b2d5e9;
}

.tool {
  position: absolute;
  top: 30px;
  left: 160px;
  right: 164px;
  height: 25px;
  background-color: #084474;
  color: #fff;
  font-size: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.img-locate {
  background-image: url('../images/toolbar/locate.png');
  background-size: 100% 100%;
  width: 24px;
  height: 24px;
  margin-top: -3px;
  cursor: pointer;
}

.img-fullView {
  background-image: url('../images/toolbar/full.png');
  background-size: 100% 100%;
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.img-outfull {
  background-image: url('../images/toolbar/outfull.png');
  background-size: 100% 100%;
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.img-closeCopy {
  background-image: url('../images/toolbar/closeCopy.png');
  background-size: 100% 100%;
  width: 24px;
  height: 24px;
  margin-top: -2px;
  display: none;
  cursor: pointer;
}

.lab-JQBH {
  width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}
.lab-JQBH-long {
  width: 245px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}
.lab-state {
  width: 80px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}
.lab-state-long {
  width: 230px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}

/********滚动条样式 S***********/
::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 8px;
}

::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  background-color: #f0c402;
  background-image: -webkit-linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
}

::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #ededed;
  border-radius: 10px;
}

/********滚动条样式 E***********/

/**
* 系统顶部
*/
.header {
  height: 27px;
  border-bottom: 2px solid #f0c402;
  background-color: #084474;
}

/**
* 系统名称
*/
.title {
  font-family: 黑体;
  font-size: 11px;
  color: #ffffff;
  font-weight: bold;
  text-shadow: 0 0 10px #529fe1, 0 0 15px #529fe1;
}

/**
* 用户图标
*/
.userImg {
  margin: 5px 3px 0 3px;
  width: 18px;
  height: 18px;
  border-radius: 100%;
  box-sizing: border-box;
  border: 1px solid #70c3fa;
  vertical-align: middle;
  object-fit: cover;
}

.userInfo {
  font-family: 黑体;
  font-size: 11px;
  color: #ffffff;
  /* font-weight: bold; */
}

.menu {
  /* vertical-align: middle; */
  /* padding: 5px; */
  font-family: 黑体;
  font-size: 11px;
  color: #ffffff;
  /* font-weight: bold; */
  cursor: pointer;
}

.menu:hover {
  /* background-color: #f0c402; */
  text-shadow: 0 0 15px #000000, 0 0 15px #000000;
}

.content-title {
  height: 15px;
  text-align: center;
  border-top: 2px solid #084474;
  border-bottom: 2px solid #084474;
  background-color: #084474;
  color: #ffffff;
  font-weight: bold;
  font-size: 12px;
}

/**
* 按钮默认样式
*/
.btn {
  margin-left: 3px;
  cursor: pointer;
  height: 25px;
  border-radius: 3px;
  font-size: 12px;
  color: #fff;
  border: 2px solid #084474;
  background-color: #084474;
}

.btn:hover {
  margin-left: 3px;
  cursor: pointer;
  height: 30px;
  border-radius: 3px;
  font-size: 12px;
  color: #fff;
  border: 2px solid #0075ff;
  background-color: #0075ff;
  text-shadow: 0 0 15px #000000, 0 0 15px #000000;
}

/**
* 文本框默认样式
*/
.txt {
  /* display: block; */
  width: 115px;
  height: 13px;
  padding: 5px 0px 5px 0px;
  margin: 3px 0px 3px 0px;
  font-size: 12px;
  line-height: 1.428571429;
  color: #555555;
  vertical-align: middle;
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out 0.15s,
    box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}

.txt:focus {
  border-color: #66afe9;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075),
    0 0 8px rgba(102, 175, 233, 0.6);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075),
    0 0 8px rgba(102, 175, 233, 0.6);
}

/****左侧模型列表类别Start*****/
.menu-grid {
  /* color: #FFF; */
  /* padding-left: 5px; */
  list-style: none;
  overflow-x: hidden;
  /* height: 100%;
  overflow-y: auto; */
  letter-spacing: 3px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.menu-grid::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 3px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 8px;
}

.menu-li-content {
  font-size: 10px;
  font-weight: 600;
  padding: 3px;
  /* margin: 1px 0px 1px -3px; */
  writing-mode: vertical-lr;
  text-orientation: upright;
  /* color:white;
    background-color: #034774; */
  border-bottom: 1px solid #529fe1;
}

.menu-li-content:hover {
  color: #fefeff;
  background-color: #529fe1;
  border-radius: 5px;
  cursor: pointer;
}

.menu-li-image {
  width: 20px;
  vertical-align: middle;
  margin: 0px 5px 0px 5px;
}

/****左侧模型列表类别End*****/

/****左侧模型列表内 S*****/
/**
* 模型列表内的类别样式
*/
.typeTitle {
  margin: 0px;
  padding: 2px;
  border-bottom: 2px solid #80b1f157;
  color: #084474;
  font-size: 12px;
  font-weight: bold;
  vertical-align: middle;
  word-break: break-all;
}

.typeModelImg {
  width: 20px;
  vertical-align: middle;
}

.app-grid {
  display: flex;
  list-style: none;
  flex-flow: row wrap;
  color: #fff;
  padding-left: 0px;
  padding-bottom: 40px;
}

.map-select {
  position: relative;
  width: 32px;
  height: 32px;
  background-repeat: no-repeat;
  background-size: 32px auto;
  text-align: center;
  margin: 5px;
  background-position: center;
  cursor: pointer;
}

.map-select:hover {
  background-size: 35px auto;
  z-index: 999;
}

/* .map-select:hover div.map-select-info {
  border-color: #089bfe;
  color: white;
  background-color: #089bfe;
  box-shadow: 0px 0px 10px 0px #089bfe;
} */

.map-select-info {
  position: relative;
  text-align: center;
  /* background-color: #0079c1; */
  width: 40px;
  font-size: 9px;
  color: rgb(0, 0, 0);
  /* margin-left: 0px; */
}

.map-select-set {
  border-color: #089bfe;
  color: white;
  background-color: #089bfe;
  box-shadow: 0px 0px 10px 0px #089bfe;
}

.li-img-content {
  padding: 5px;
  width: 100%;
  display: flex;
}

.li-img-content:hover {
  background-color: #f0c402;
  box-shadow: 0px 0px 10px 0px #f0c402;
}

.li-img-close {
  vertical-align: middle;
  position: absolute;
  right: 10px;
  margin-top: 13px;
  width: 25px;
  height: 25px;
  cursor: pointer;
}

.li-img-share {
  vertical-align: middle;
  position: absolute;
  right: 45px;
  margin-top: 13px;
  width: 25px;
  height: 25px;
  cursor: pointer;
  /* display: none; */
}

/* 
.li-img-content:hover .li-img-close {
  display: block;
} */

/****左侧模型列表内 E*****/

/*********场景列表 S**********/

.ol-scene-grid {
  text-align: center;
  list-style: none;
  flex-flow: row wrap;
  color: #fff;
  padding: 0px;
  /* padding: 5px 15px 5px 15px; */
}

.li-scene-content {
  padding: 5px;
  text-align: center;
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 150px;
}

.li-scene-content:hover {
  background-color: #529fe1;
  box-shadow: 0px 0px 10px 0px #084474;
}

.sp-scene-content {
  display: grid;
  align-content: flex-end;
  justify-items: center;
}

.li-scene-img {
  text-align: center;
  /* padding: 5px 5px 5px 5px; */
  border: 1px solid #afafaf;
  background-color: #f8f8f8;
}

.li-scene-close {
  position: absolute;
  right: 10px;
  cursor: pointer;
  width: 30px;
  height: 30px;
  /* display: none; */
}

/* .li-scene-content:hover .li-scene-close {
  display: block;
} */

/*********场景列表 E**********/

/****右侧列表 S*****/
.td-title {
  text-align: right;
  font-family: 'å¾®è½¯é›…é»‘';
  font-size: 12px;
  padding: 0px 5px;
  min-width: 24px;
}

#bigDiv {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(24, 23, 23, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.li-fill-Img {
  padding: 10px 10px 10px 10px;
}

.li-fill-select {
  background-color: rgba(0, 150, 255, 0.8);
  border-radius: 6px;
}

.li-fill-Img:hover {
  background-color: rgba(0, 150, 255, 0.5);
  border-radius: 6px;
}

.layui-slider-input {
  width: 70px;
  height: 22px;
  border: 1px solid #eee;
  border-radius: 3px;
  font-size: 14px;
  line-height: 22px;
  position: absolute;
  right: 1px;
  top: -10px;
}

/****右侧列表 E*****/

/****底部信息栏*****/
.userline {
  position: absolute;
  bottom: 0px;
  right: 164px;
  width: 140px;
  font-size: 12px;
  border: 1px solid #f0c402;
}

.userline .userlegend {
  height: 25px;
  width: 170px;
  background-color: #084474;
  color: #fff;
  text-align: center;
  font-size: 14px;
}

.legend {
  height: 22px;
  width: 130px;
  background-color: #084474;
  color: #fff;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  padding: 0px 5px 0px 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statebar {
  position: absolute;
  bottom: 0px;
  left: 161px;
  right: 370px;
  height: 24px;
  background-color: #084474;
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 15px;
  display: flex;
}

.bottomNav {
  position: absolute;
  bottom: 18px;
  right: 180px;
  display: flex;
  flex-direction: column;
}
.bottomNav .layui-btn {
  margin-bottom: 12px;
  margin-left: 0;
}

.bottomNav .layui-bg-blue {
  background-color: #0084ff !important;
}
.bottomNav .layui-bg-green {
  background-color: #34c759 !important;
}

.copyName {
  background-color: #1e9fff;
  border-radius: 5px;
  padding: 5px 10px 5px 10px;
  color: rgb(255, 255, 255);
  font-weight: 400;
  cursor: pointer;
  font-size: 13px;
  line-height: 28px;
}

/****底部信息栏*****/

/****弹窗字号*****/
.layui-layer-dialog .layui-layer-content {
  font-size: 10px !important;
}

.layui-laypage-next em,
.layui-laypage-prev em {
  font-size: 12px !important;
}

.layui-laypage a,
.layui-laypage span {
  padding: 0 6px !important;
}

.layui-laypage .layui-laypage-count,
.layui-laypage .layui-laypage-limits,
.layui-laypage .layui-laypage-refresh,
.layui-laypage .layui-laypage-skip {
  margin-left: 1px !important;
  margin-right: -5px !important;
}

.layui-laypage select {
  height: 29px !important;
  padding: 0px !important;
}

/****弹窗字号*****/

/* 模型列表容器  */
.modelList-container {
  position: fixed;
  width: 160px;
  top: 29px;
  bottom: 0px;
  left: 0px;
  background-color: white;
  border: 1px solid #084474;
}
.leftmenu {
  position: absolute;
  bottom: 0px;
  top: 20px;
  width: 24px;
  /* border-right: 1px solid #084474; */
  z-index: 1;
  background: #f9f9f9;
  /* display: none; */
  height: calc(100vh - 50px);
  overflow-y: auto;
}

.search-container {
  position: absolute;
  top: 18px;
  left: 32px;
  height: 20px;
}

.leftmodel-container {
  position: absolute;
  bottom: 0px;
  top: 60px;
  left: 24px;
  overflow-y: auto;
  width: 135px;
}

/* 地图容器 */
.view-container {
  position: fixed;
  top: 29px;
  left: 160px;
  bottom: 0px;
  right: 160px;
}

/* 属性信息容器  */
.attribute-container {
  position: fixed;
  width: 160px;
  top: 29px;
  bottom: 0px;
  right: 0px;
  background-color: white;
  border: 2px solid #084474;
}

.attribute-legend {
  font-size: 12px !important;
  padding: 5px 0px 5px 0px !important;
}

/* 图层列表容器 */
.layer-container {
  position: fixed;
  width: 160px;
  top: 29px;
  bottom: 0px;
  left: 0px;
  background-color: white;
  border: 2px solid #084474;
  z-index: 2;
  display: none;
}

/* 副本列表容器  */
.scene-container {
  position: fixed;
  width: 167px;
  top: 29px;
  bottom: 0px;
  left: 0px;
  background-color: white;
  border: 2px solid #084474;
  z-index: 2;
  display: none;
}

.layui-tab-brief > .layui-tab-more li.layui-this:after,
.layui-tab-brief > .layui-tab-title .layui-this:after {
  border: none;
  border-radius: 0;
  border-bottom: 2px solid #084474 !important;
}

.layui-tab-brief > .layui-tab-title .layui-this {
  color: #084474 !important;
}
