html,
body,
#viewDiv {
  padding: 0;
  margin: 0;
}

/*去掉地图黑边框*/
.esri-view-surface--inset-outline:focus::after {
  outline: none !important;
}

.layui-form-checked[lay-skin='primary'] i {
  border-color: #529fe1 !important;
  background-color: #529fe1;
  color: #fff;
}

.layui-icon-color {
  color: #b2d5e9;
}

/********滚动条样式 S***********/
::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 8px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 8px;
}

::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  background-color: #f0c402;
  background-image: -webkit-linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
}

::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #ededed;
  border-radius: 10px;
}

/********滚动条样式 E***********/

/**
* 系统顶部
*/
.header {
  height: 57px;
  border-bottom: 5px solid #f0c402;
  background-color: #084474;
}

/**
* 系统名称
*/
.title {
  font-family: 黑体;
  font-size: 24px;
  color: #ffffff;
  font-weight: bold;
  text-shadow: 0 0 15px #529fe1, 0 0 15px #529fe1;
  margin-right: 100px;
}

/**
* 用户图标
*/
.userImg {
  margin: 10px 8px 0 5px;
  width: 40px;
  height: 40px;
  border-radius: 100%;
  box-sizing: border-box;
  border: 3px solid #70c3fa;
  vertical-align: middle;
  object-fit: cover;
}

.menu {
  vertical-align: middle;

  padding: 20px 20px 20px 20px;
  font-family: 黑体;
  font-size: 18px;
  color: #ffffff;
  font-weight: bold;
  cursor: pointer;
}

.menu:hover {
  background-color: #f0c402;
  text-shadow: 0 0 15px #000000, 0 0 15px #000000;
}

.content-title {
  height: 25px;
  text-align: center;
  border-top: 2px solid #084474;
  border-bottom: 2px solid #084474;
  padding-top: 5px;
  background-color: #084474;
  color: #ffffff;
  font-weight: bold;
}

/**
* 按钮默认样式
*/
.btn {
  margin-left: 3px;
  cursor: pointer;
  height: 30px;
  border-radius: 3px;
  font-size: 12px;
  color: #fff;
  border: 2px solid #084474;
  background-color: #084474;
}

.btn:hover {
  margin-left: 3px;
  cursor: pointer;
  height: 30px;
  border-radius: 3px;
  font-size: 12px;
  color: #fff;
  border: 2px solid #069bf1;
  background-color: #069bf1;
  text-shadow: 0 0 15px #000000, 0 0 15px #000000;
}

/**
* 文本框默认样式
*/
.txt {
  /* display: block; */
  width: 150px;
  height: 15px;
  padding: 6px 12px;
  margin: 3px 0px 3px 0px;
  font-size: 12px;
  line-height: 1.428571429;
  color: #555555;
  vertical-align: middle;
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out 0.15s,
    box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}

.txt:focus {
  border-color: #66afe9;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075),
    0 0 8px rgba(102, 175, 233, 0.6);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075),
    0 0 8px rgba(102, 175, 233, 0.6);
}

/****左侧模型列表类别Start*****/
.menu-grid {
  /* color: #FFF; */
  padding-left: 5px;
  list-style: none;
  overflow-x: hidden;
  /* height: 100%;
  overflow-y: auto; */
  letter-spacing: 5px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.menu-grid::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 3px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 8px;
}

.menu-li-content {
  font-size: 14px;
  padding: 5px;
  margin: 5px 5px 5px 0px;
  writing-mode: vertical-lr;
  text-orientation: upright;
  /* color:white;
    background-color: #034774; */
  border-bottom: 1px solid #529fe1;
}

.menu-li-content:hover {
  color: #fefeff;
  background-color: #529fe1;
  border-radius: 5px;
  cursor: pointer;
}

.menu-li-image {
  width: 24px;
  vertical-align: middle;
  margin: 0px 5px 0px 3px;
}

/****左侧模型列表类别End*****/

/****左侧模型列表内 S*****/
/**
* 模型列表内的类别样式
*/
.typeTitle {
  margin: 0px;
  padding: 20px 0px 5px 10px;
  /* border-top: 2px solid #80b1f157; */
  border-bottom: 2px solid #80b1f157;
  /* background-color: #084474; */
  font-weight: normal;
  color: #084474;
  font-size: 20px;
  font-weight: bold;
  vertical-align: middle;
  word-break: break-all;
}

.typeModelImg {
  width: 25px;
  vertical-align: middle;
}

.app-grid {
  display: flex;
  list-style: none;
  flex-flow: row wrap;
  color: #fff;
  padding-left: 8px;
}

.map-select {
  position: relative;
  width: 70px;
  height: 70px;
  background-repeat: no-repeat;
  background-size: 60px auto;
  text-align: center;
  margin: 10px;
  background-position: center;
  cursor: pointer;
}

.map-select:hover {
  background-size: 65px auto;
  z-index: 999;
}

/* .map-select:hover  {
  border-color: #089bfe;
  color: white;
  background-color: #089bfe;
  box-shadow: 0px 0px 10px 0px #089bfe;
} */

.map-select-info {
  position: relative;
  text-align: center;
  /* background-color: #0079c1; */
  width: 70px;
  font-size: 12px;
  color: rgb(0, 0, 0);
  margin-left: 10px;
}

.map-select-set {
  border-color: #089bfe;
  color: white;
  background-color: #089bfe;
  box-shadow: 0px 0px 10px 0px #089bfe;
}

.li-img-content {
  padding: 5px;
  width: 100%;
  display: flex;
}

.li-img-content:hover {
  background-color: #f0c402;
  box-shadow: 0px 0px 10px 0px #f0c402;
}

.li-img-close {
  vertical-align: middle;
  position: absolute;
  right: 15px;
  margin-top: 13px;
  width: 25px;
  height: 25px;
  cursor: pointer;
  display: none;
}

.li-img-share {
  vertical-align: middle;
  position: absolute;
  right: 45px;
  margin-top: 13px;
  width: 25px;
  height: 25px;
  cursor: pointer;
  display: none;
}

.li-img-content:hover .li-img-close,
.li-img-content:hover .li-img-share {
  display: block;
}

/****左侧模型列表内 E*****/

/*********场景列表 S**********/

.ol-scene-grid {
  text-align: center;
  list-style: none;
  flex-flow: row wrap;
  color: #fff;
  padding: 0px;
  /* padding: 5px 15px 5px 15px; */
}

.li-scene-content {
  padding: 10px 18px 5px 15px;
  text-align: center;
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 290px;
}

.li-scene-content:hover {
  background-color: #529fe1;
  box-shadow: 0px 0px 10px 0px #084474;
}

.sp-scene-content {
  display: grid;
  align-content: flex-end;
  justify-items: center;
}

.li-scene-img {
  text-align: center;
  padding: 5px 5px 5px 5px;
  border: 1px solid #afafaf;
  background-color: #f8f8f8;
}

.li-scene-close {
  position: absolute;
  right: 20px;
  cursor: pointer;
  width: 30px;
  height: 30px;
  /* display: none; */
}

/* .li-scene-content:hover .li-scene-close {
  display: block;
} */

/*********场景列表 E**********/

/****右侧列表 S*****/
.td-title {
  text-align: right;
  font-family: '微软雅黑';
  font-size: 14px;
  width: 90px;
}

#bigDiv {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(24, 23, 23, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.li-fill-Img {
  padding: 10px 10px 10px 10px;
}

.li-fill-select {
  background-color: rgba(0, 150, 255, 0.8);
  border-radius: 6px;
}

.li-fill-Img:hover {
  background-color: rgba(0, 150, 255, 0.5);
  border-radius: 6px;
}

/****右侧列表 E*****/

/****中部信息栏*****/
.tool {
  position: absolute;
  top: 60px;
  left: 390px;
  right: 300px;
  height: 40px;
  background-color: #084474;
  color: #fff;
  font-size: 18px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.img-locate {
  background-image: url('../images/toolbar/locate.png');
  width: 32px;
  height: 32px;
  margin-top: -3px;
  cursor: pointer;
}

.img-fullView {
  background-image: url('../images/toolbar/full.png');
  width: 32px;
  height: 32px;
  cursor: pointer;
}

.img-outfull {
  background-image: url('../images/toolbar/outfull.png');
  width: 32px;
  height: 32px;
  cursor: pointer;
}

.img-closeCopy {
  background-image: url('../images/toolbar/closeCopy.png');
  margin-left: 10px;
  width: 32px;
  height: 32px;
  display: none;
  cursor: pointer;
}

.userline {
  position: absolute;
  bottom: 0px;
  right: 295px;
  width: 180px;
  font-size: 16px;
  border: 1px solid #f0c402;
}

.legend {
  height: 30px;
  width: 170px;
  background-color: #084474;
  color: #fff;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  padding: 0px 5px 0px 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statebar {
  position: absolute;
  bottom: 0px;
  left: 330px;
  right: 482px;
  height: 30px;
  background-color: #084474;
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 18px;
}

.bottomNav {
  position: absolute;
  bottom: 18px;
  right: 300px;
  display: flex;
  flex-direction: column;
}
.bottomNav .layui-btn {
  margin-bottom: 12px;
  margin-left: 0;
}

.bottomNav .layui-bg-blue {
  background-color: #0084ff !important;
}
.bottomNav .layui-bg-green {
  background-color: #34c759 !important;
}

.copyName {
  background-color: #1e9fff;
  border-radius: 5px;
  padding: 5px 10px 5px 10px;
  color: rgb(255, 255, 255);
  font-weight: 400;
  cursor: pointer;
  font-size: 13px;
  line-height: 28px;
}

/****底部信息栏*****/

/****弹窗字号*****/
.layui-layer-dialog .layui-layer-content {
  font-size: 12px !important;
}

.layui-laypage-next em,
.layui-laypage-prev em {
  font-size: 12px !important;
}

/* 自定义复选框的外观样式 */
.custom-checkbox input[type='checkbox'] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  -o-appearance: none;
  width: 16px;
  height: 16px;
  position: relative;
  border: 1px solid #ccc;
  border-radius: 3px;
}

/* 自定义复选框的选中状态样式 */
.custom-checkbox input[type='checkbox']:checked::before {
  content: '\2713';
  /* 使用 Unicode 编码表示√符号 */
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 14px;
  font-weight: bold;
  color: #fff;
}

/* 自定义复选框的背景颜色 */
.custom-checkbox input[type='checkbox']:checked {
  background-color: #007bff;
  border-color: #007bff;
}

/* 自定义单选按钮的外观样式 */
.custom-radio input[type='radio'] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  -o-appearance: none;
  width: 16px;
  height: 16px;
  position: relative;
  border: 1px solid #ccc;
  border-radius: 50%;
}

/* 自定义单选按钮的选中状态样式 */
.custom-radio input[type='radio']:checked::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #007bff;
  mask: radial-gradient(circle at center, transparent 20%, #000 21%);
}
/*********/

.layui-tab-brief > .layui-tab-more li.layui-this:after,
.layui-tab-brief > .layui-tab-title .layui-this:after {
  border: none;
  border-radius: 0;
  border-bottom: 2px solid #084474 !important;
}

.layui-tab-brief > .layui-tab-title .layui-this {
  color: #084474 !important;
}
