{"asset": {"generator": "FBX2glTF v0.9.7", "version": "2.0"}, "scene": 0, "extensionsUsed": ["KHR_lights_punctual"], "buffers": [{"byteLength": 16525420, "uri": "buffer.bin"}], "bufferViews": [{"buffer": 0, "byteLength": 56232, "byteOffset": 0, "target": 34963}, {"buffer": 0, "byteLength": 204816, "byteOffset": 56232, "target": 34962}, {"buffer": 0, "byteLength": 204816, "byteOffset": 261048, "target": 34962}, {"buffer": 0, "byteLength": 136544, "byteOffset": 465864, "target": 34962}, {"buffer": 0, "byteLength": 136544, "byteOffset": 602408, "target": 34962}, {"buffer": 0, "byteLength": 42072, "byteOffset": 738952, "target": 34963}, {"buffer": 0, "byteLength": 165564, "byteOffset": 781024, "target": 34962}, {"buffer": 0, "byteLength": 165564, "byteOffset": 946588, "target": 34962}, {"buffer": 0, "byteLength": 110376, "byteOffset": 1112152, "target": 34962}, {"buffer": 0, "byteLength": 110376, "byteOffset": 1222528, "target": 34962}, {"buffer": 0, "byteLength": 13500, "byteOffset": 1332904, "target": 34963}, {"buffer": 0, "byteLength": 47436, "byteOffset": 1346404, "target": 34962}, {"buffer": 0, "byteLength": 47436, "byteOffset": 1393840, "target": 34962}, {"buffer": 0, "byteLength": 31624, "byteOffset": 1441276, "target": 34962}, {"buffer": 0, "byteLength": 31624, "byteOffset": 1472900, "target": 34962}, {"buffer": 0, "byteLength": 19920, "byteOffset": 1504524, "target": 34963}, {"buffer": 0, "byteLength": 77448, "byteOffset": 1524444, "target": 34962}, {"buffer": 0, "byteLength": 77448, "byteOffset": 1601892, "target": 34962}, {"buffer": 0, "byteLength": 51632, "byteOffset": 1679340, "target": 34962}, {"buffer": 0, "byteLength": 51632, "byteOffset": 1730972, "target": 34962}, {"buffer": 0, "byteLength": 19920, "byteOffset": 1782604, "target": 34963}, {"buffer": 0, "byteLength": 76608, "byteOffset": 1802524, "target": 34962}, {"buffer": 0, "byteLength": 76608, "byteOffset": 1879132, "target": 34962}, {"buffer": 0, "byteLength": 51072, "byteOffset": 1955740, "target": 34962}, {"buffer": 0, "byteLength": 51072, "byteOffset": 2006812, "target": 34962}, {"buffer": 0, "byteLength": 13500, "byteOffset": 2057884, "target": 34963}, {"buffer": 0, "byteLength": 43392, "byteOffset": 2071384, "target": 34962}, {"buffer": 0, "byteLength": 43392, "byteOffset": 2114776, "target": 34962}, {"buffer": 0, "byteLength": 28928, "byteOffset": 2158168, "target": 34962}, {"buffer": 0, "byteLength": 28928, "byteOffset": 2187096, "target": 34962}, {"buffer": 0, "byteLength": 4860, "byteOffset": 2216024, "target": 34963}, {"buffer": 0, "byteLength": 17088, "byteOffset": 2220884, "target": 34962}, {"buffer": 0, "byteLength": 17088, "byteOffset": 2237972, "target": 34962}, {"buffer": 0, "byteLength": 11392, "byteOffset": 2255060, "target": 34962}, {"buffer": 0, "byteLength": 11392, "byteOffset": 2266452, "target": 34962}, {"buffer": 0, "byteLength": 42072, "byteOffset": 2277844, "target": 34963}, {"buffer": 0, "byteLength": 163656, "byteOffset": 2319916, "target": 34962}, {"buffer": 0, "byteLength": 163656, "byteOffset": 2483572, "target": 34962}, {"buffer": 0, "byteLength": 109104, "byteOffset": 2647228, "target": 34962}, {"buffer": 0, "byteLength": 109104, "byteOffset": 2756332, "target": 34962}, {"buffer": 0, "byteLength": 4860, "byteOffset": 2865436, "target": 34963}, {"buffer": 0, "byteLength": 17544, "byteOffset": 2870296, "target": 34962}, {"buffer": 0, "byteLength": 17544, "byteOffset": 2887840, "target": 34962}, {"buffer": 0, "byteLength": 11696, "byteOffset": 2905384, "target": 34962}, {"buffer": 0, "byteLength": 11696, "byteOffset": 2917080, "target": 34962}, {"buffer": 0, "byteLength": 34824, "byteOffset": 2928776, "target": 34963}, {"buffer": 0, "byteLength": 201720, "byteOffset": 2963600, "target": 34962}, {"buffer": 0, "byteLength": 201720, "byteOffset": 3165320, "target": 34962}, {"buffer": 0, "byteLength": 134480, "byteOffset": 3367040, "target": 34962}, {"buffer": 0, "byteLength": 134480, "byteOffset": 3501520, "target": 34962}, {"buffer": 0, "byteLength": 18432, "byteOffset": 3636000, "target": 34963}, {"buffer": 0, "byteLength": 73920, "byteOffset": 3654432, "target": 34962}, {"buffer": 0, "byteLength": 73920, "byteOffset": 3728352, "target": 34962}, {"buffer": 0, "byteLength": 49280, "byteOffset": 3802272, "target": 34962}, {"buffer": 0, "byteLength": 49280, "byteOffset": 3851552, "target": 34962}, {"buffer": 0, "byteLength": 1080, "byteOffset": 3900832, "target": 34963}, {"buffer": 0, "byteLength": 4320, "byteOffset": 3901912, "target": 34962}, {"buffer": 0, "byteLength": 4320, "byteOffset": 3906232, "target": 34962}, {"buffer": 0, "byteLength": 2880, "byteOffset": 3910552, "target": 34962}, {"buffer": 0, "byteLength": 2880, "byteOffset": 3913432, "target": 34962}, {"buffer": 0, "byteLength": 1080, "byteOffset": 3916312, "target": 34963}, {"buffer": 0, "byteLength": 4320, "byteOffset": 3917392, "target": 34962}, {"buffer": 0, "byteLength": 4320, "byteOffset": 3921712, "target": 34962}, {"buffer": 0, "byteLength": 2880, "byteOffset": 3926032, "target": 34962}, {"buffer": 0, "byteLength": 2880, "byteOffset": 3928912, "target": 34962}, {"buffer": 0, "byteLength": 1248, "byteOffset": 3931792, "target": 34963}, {"buffer": 0, "byteLength": 4992, "byteOffset": 3933040, "target": 34962}, {"buffer": 0, "byteLength": 4992, "byteOffset": 3938032, "target": 34962}, {"buffer": 0, "byteLength": 3328, "byteOffset": 3943024, "target": 34962}, {"buffer": 0, "byteLength": 3328, "byteOffset": 3946352, "target": 34962}, {"buffer": 0, "byteLength": 2160, "byteOffset": 3949680, "target": 34963}, {"buffer": 0, "byteLength": 8640, "byteOffset": 3951840, "target": 34962}, {"buffer": 0, "byteLength": 8640, "byteOffset": 3960480, "target": 34962}, {"buffer": 0, "byteLength": 5760, "byteOffset": 3969120, "target": 34962}, {"buffer": 0, "byteLength": 5760, "byteOffset": 3974880, "target": 34962}, {"buffer": 0, "byteLength": 118248, "byteOffset": 3980640, "target": 34963}, {"buffer": 0, "byteLength": 658920, "byteOffset": 4098888, "target": 34962}, {"buffer": 0, "byteLength": 658920, "byteOffset": 4757808, "target": 34962}, {"buffer": 0, "byteLength": 439280, "byteOffset": 5416728, "target": 34962}, {"buffer": 0, "byteLength": 439280, "byteOffset": 5856008, "target": 34962}, {"buffer": 0, "byteLength": 7056, "byteOffset": 6295288, "target": 34963}, {"buffer": 0, "byteLength": 28320, "byteOffset": 6302344, "target": 34962}, {"buffer": 0, "byteLength": 28320, "byteOffset": 6330664, "target": 34962}, {"buffer": 0, "byteLength": 18880, "byteOffset": 6358984, "target": 34962}, {"buffer": 0, "byteLength": 18880, "byteOffset": 6377864, "target": 34962}, {"buffer": 0, "byteLength": 11040, "byteOffset": 6396744, "target": 34963}, {"buffer": 0, "byteLength": 65568, "byteOffset": 6407784, "target": 34962}, {"buffer": 0, "byteLength": 65568, "byteOffset": 6473352, "target": 34962}, {"buffer": 0, "byteLength": 43712, "byteOffset": 6538920, "target": 34962}, {"buffer": 0, "byteLength": 43712, "byteOffset": 6582632, "target": 34962}, {"buffer": 0, "byteLength": 29712, "byteOffset": 6626344, "target": 34963}, {"buffer": 0, "byteLength": 177912, "byteOffset": 6656056, "target": 34962}, {"buffer": 0, "byteLength": 177912, "byteOffset": 6833968, "target": 34962}, {"buffer": 0, "byteLength": 118608, "byteOffset": 7011880, "target": 34962}, {"buffer": 0, "byteLength": 118608, "byteOffset": 7130488, "target": 34962}, {"buffer": 0, "byteLength": 489084, "byteOffset": 7249096, "target": 34963}, {"buffer": 0, "byteLength": 1235508, "byteOffset": 7738180, "target": 34962}, {"buffer": 0, "byteLength": 1235508, "byteOffset": 8973688, "target": 34962}, {"buffer": 0, "byteLength": 823672, "byteOffset": 10209196, "target": 34962}, {"buffer": 0, "byteLength": 823672, "byteOffset": 11032868, "target": 34962}, {"buffer": 0, "byteLength": 63540, "byteOffset": 11856540, "target": 34963}, {"buffer": 0, "byteLength": 354372, "byteOffset": 11920080, "target": 34962}, {"buffer": 0, "byteLength": 354372, "byteOffset": 12274452, "target": 34962}, {"buffer": 0, "byteLength": 236248, "byteOffset": 12628824, "target": 34962}, {"buffer": 0, "byteLength": 236248, "byteOffset": 12865072, "target": 34962}, {"buffer": 0, "byteLength": 16956, "byteOffset": 13101320, "target": 34963}, {"buffer": 0, "byteLength": 67944, "byteOffset": 13118276, "target": 34962}, {"buffer": 0, "byteLength": 67944, "byteOffset": 13186220, "target": 34962}, {"buffer": 0, "byteLength": 45296, "byteOffset": 13254164, "target": 34962}, {"buffer": 0, "byteLength": 45296, "byteOffset": 13299460, "target": 34962}, {"buffer": 0, "byteLength": 144, "byteOffset": 13344756, "target": 34963}, {"buffer": 0, "byteLength": 576, "byteOffset": 13344900, "target": 34962}, {"buffer": 0, "byteLength": 576, "byteOffset": 13345476, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 13346052, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 13346436, "target": 34962}, {"buffer": 0, "byteLength": 16314, "byteOffset": 13346820, "target": 34963}, {"buffer": 0, "byteLength": 64548, "byteOffset": 13363136, "target": 34962}, {"buffer": 0, "byteLength": 64548, "byteOffset": 13427684, "target": 34962}, {"buffer": 0, "byteLength": 43032, "byteOffset": 13492232, "target": 34962}, {"buffer": 0, "byteLength": 43032, "byteOffset": 13535264, "target": 34962}, {"buffer": 0, "byteLength": 3480, "byteOffset": 13578296, "target": 34963}, {"buffer": 0, "byteLength": 18864, "byteOffset": 13581776, "target": 34962}, {"buffer": 0, "byteLength": 18864, "byteOffset": 13600640, "target": 34962}, {"buffer": 0, "byteLength": 12576, "byteOffset": 13619504, "target": 34962}, {"buffer": 0, "byteLength": 12576, "byteOffset": 13632080, "target": 34962}, {"buffer": 0, "byteLength": 744, "byteOffset": 13644656, "target": 34963}, {"buffer": 0, "byteLength": 2976, "byteOffset": 13645400, "target": 34962}, {"buffer": 0, "byteLength": 2976, "byteOffset": 13648376, "target": 34962}, {"buffer": 0, "byteLength": 1984, "byteOffset": 13651352, "target": 34962}, {"buffer": 0, "byteLength": 1984, "byteOffset": 13653336, "target": 34962}, {"buffer": 0, "byteLength": 8286, "byteOffset": 13655320, "target": 34963}, {"buffer": 0, "byteLength": 49212, "byteOffset": 13663608, "target": 34962}, {"buffer": 0, "byteLength": 49212, "byteOffset": 13712820, "target": 34962}, {"buffer": 0, "byteLength": 32808, "byteOffset": 13762032, "target": 34962}, {"buffer": 0, "byteLength": 32808, "byteOffset": 13794840, "target": 34962}, {"buffer": 0, "byteLength": 6096, "byteOffset": 13827648, "target": 34963}, {"buffer": 0, "byteLength": 24504, "byteOffset": 13833744, "target": 34962}, {"buffer": 0, "byteLength": 24504, "byteOffset": 13858248, "target": 34962}, {"buffer": 0, "byteLength": 16336, "byteOffset": 13882752, "target": 34962}, {"buffer": 0, "byteLength": 16336, "byteOffset": 13899088, "target": 34962}, {"buffer": 0, "byteLength": 5760, "byteOffset": 13915424, "target": 34963}, {"buffer": 0, "byteLength": 23040, "byteOffset": 13921184, "target": 34962}, {"buffer": 0, "byteLength": 23040, "byteOffset": 13944224, "target": 34962}, {"buffer": 0, "byteLength": 15360, "byteOffset": 13967264, "target": 34962}, {"buffer": 0, "byteLength": 15360, "byteOffset": 13982624, "target": 34962}, {"buffer": 0, "byteLength": 5760, "byteOffset": 13997984, "target": 34963}, {"buffer": 0, "byteLength": 23040, "byteOffset": 14003744, "target": 34962}, {"buffer": 0, "byteLength": 23040, "byteOffset": 14026784, "target": 34962}, {"buffer": 0, "byteLength": 15360, "byteOffset": 14049824, "target": 34962}, {"buffer": 0, "byteLength": 15360, "byteOffset": 14065184, "target": 34962}, {"buffer": 0, "byteLength": 3480, "byteOffset": 14080544, "target": 34963}, {"buffer": 0, "byteLength": 18744, "byteOffset": 14084024, "target": 34962}, {"buffer": 0, "byteLength": 18744, "byteOffset": 14102768, "target": 34962}, {"buffer": 0, "byteLength": 12496, "byteOffset": 14121512, "target": 34962}, {"buffer": 0, "byteLength": 12496, "byteOffset": 14134008, "target": 34962}, {"buffer": 0, "byteLength": 12432, "byteOffset": 14146504, "target": 34963}, {"buffer": 0, "byteLength": 49728, "byteOffset": 14158936, "target": 34962}, {"buffer": 0, "byteLength": 49728, "byteOffset": 14208664, "target": 34962}, {"buffer": 0, "byteLength": 33152, "byteOffset": 14258392, "target": 34962}, {"buffer": 0, "byteLength": 33152, "byteOffset": 14291544, "target": 34962}, {"buffer": 0, "byteLength": 11520, "byteOffset": 14324696, "target": 34963}, {"buffer": 0, "byteLength": 46080, "byteOffset": 14336216, "target": 34962}, {"buffer": 0, "byteLength": 46080, "byteOffset": 14382296, "target": 34962}, {"buffer": 0, "byteLength": 30720, "byteOffset": 14428376, "target": 34962}, {"buffer": 0, "byteLength": 30720, "byteOffset": 14459096, "target": 34962}, {"buffer": 0, "byteLength": 4440, "byteOffset": 14489816, "target": 34963}, {"buffer": 0, "byteLength": 23496, "byteOffset": 14494256, "target": 34962}, {"buffer": 0, "byteLength": 23496, "byteOffset": 14517752, "target": 34962}, {"buffer": 0, "byteLength": 15664, "byteOffset": 14541248, "target": 34962}, {"buffer": 0, "byteLength": 15664, "byteOffset": 14556912, "target": 34962}, {"buffer": 0, "byteLength": 4440, "byteOffset": 14572576, "target": 34963}, {"buffer": 0, "byteLength": 22824, "byteOffset": 14577016, "target": 34962}, {"buffer": 0, "byteLength": 22824, "byteOffset": 14599840, "target": 34962}, {"buffer": 0, "byteLength": 15216, "byteOffset": 14622664, "target": 34962}, {"buffer": 0, "byteLength": 15216, "byteOffset": 14637880, "target": 34962}, {"buffer": 0, "byteLength": 5520, "byteOffset": 14653096, "target": 34963}, {"buffer": 0, "byteLength": 22176, "byteOffset": 14658616, "target": 34962}, {"buffer": 0, "byteLength": 22176, "byteOffset": 14680792, "target": 34962}, {"buffer": 0, "byteLength": 14784, "byteOffset": 14702968, "target": 34962}, {"buffer": 0, "byteLength": 14784, "byteOffset": 14717752, "target": 34962}, {"buffer": 0, "byteLength": 17280, "byteOffset": 14732536, "target": 34963}, {"buffer": 0, "byteLength": 103680, "byteOffset": 14749816, "target": 34962}, {"buffer": 0, "byteLength": 103680, "byteOffset": 14853496, "target": 34962}, {"buffer": 0, "byteLength": 69120, "byteOffset": 14957176, "target": 34962}, {"buffer": 0, "byteLength": 69120, "byteOffset": 15026296, "target": 34962}, {"buffer": 0, "byteLength": 12432, "byteOffset": 15095416, "target": 34963}, {"buffer": 0, "byteLength": 49584, "byteOffset": 15107848, "target": 34962}, {"buffer": 0, "byteLength": 49584, "byteOffset": 15157432, "target": 34962}, {"buffer": 0, "byteLength": 33056, "byteOffset": 15207016, "target": 34962}, {"buffer": 0, "byteLength": 33056, "byteOffset": 15240072, "target": 34962}, {"buffer": 0, "byteLength": 5760, "byteOffset": 15273128, "target": 34963}, {"buffer": 0, "byteLength": 34560, "byteOffset": 15278888, "target": 34962}, {"buffer": 0, "byteLength": 34560, "byteOffset": 15313448, "target": 34962}, {"buffer": 0, "byteLength": 23040, "byteOffset": 15348008, "target": 34962}, {"buffer": 0, "byteLength": 23040, "byteOffset": 15371048, "target": 34962}, {"buffer": 0, "byteLength": 1272, "byteOffset": 15394088, "target": 34963}, {"buffer": 0, "byteLength": 4944, "byteOffset": 15395360, "target": 34962}, {"buffer": 0, "byteLength": 4944, "byteOffset": 15400304, "target": 34962}, {"buffer": 0, "byteLength": 3296, "byteOffset": 15405248, "target": 34962}, {"buffer": 0, "byteLength": 3296, "byteOffset": 15408544, "target": 34962}, {"buffer": 0, "byteLength": 7872, "byteOffset": 15411840, "target": 34963}, {"buffer": 0, "byteLength": 26976, "byteOffset": 15419712, "target": 34962}, {"buffer": 0, "byteLength": 26976, "byteOffset": 15446688, "target": 34962}, {"buffer": 0, "byteLength": 17984, "byteOffset": 15473664, "target": 34962}, {"buffer": 0, "byteLength": 17984, "byteOffset": 15491648, "target": 34962}, {"buffer": 0, "byteLength": 4800, "byteOffset": 15509632, "target": 34963}, {"buffer": 0, "byteLength": 19200, "byteOffset": 15514432, "target": 34962}, {"buffer": 0, "byteLength": 19200, "byteOffset": 15533632, "target": 34962}, {"buffer": 0, "byteLength": 12800, "byteOffset": 15552832, "target": 34962}, {"buffer": 0, "byteLength": 12800, "byteOffset": 15565632, "target": 34962}, {"buffer": 0, "byteLength": 54708, "byteOffset": 15578432, "target": 34963}, {"buffer": 0, "byteLength": 267684, "byteOffset": 15633140, "target": 34962}, {"buffer": 0, "byteLength": 267684, "byteOffset": 15900824, "target": 34962}, {"buffer": 0, "byteLength": 178456, "byteOffset": 16168508, "target": 34962}, {"buffer": 0, "byteLength": 178456, "byteOffset": 16346964, "target": 34962}], "scenes": [{"name": "Root Scene", "nodes": [0]}], "accessors": [{"componentType": 5123, "type": "SCALAR", "count": 28116, "bufferView": 0, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 17068, "bufferView": 1, "byteOffset": 0, "min": [-1.27609395980835, 1.26557326316833, 0.433172821998596], "max": [1.27609527111053, 5.90305328369141, 2.50329494476318]}, {"componentType": 5126, "type": "VEC3", "count": 17068, "bufferView": 2, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 17068, "bufferView": 3, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 17068, "bufferView": 4, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 21036, "bufferView": 5, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 13797, "bufferView": 6, "byteOffset": 0, "min": [0.45595845580101, -0.306409955024719, -0.304731279611588], "max": [1.24993395805359, 0.306409537792206, 0.304731130599976]}, {"componentType": 5126, "type": "VEC3", "count": 13797, "bufferView": 7, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 13797, "bufferView": 8, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 13797, "bufferView": 9, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 6750, "bufferView": 10, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 3953, "bufferView": 11, "byteOffset": 0, "min": [1.24439382553101, -0.1850396245718, -0.184025779366493], "max": [1.27348220348358, 0.18503899872303, 0.184025481343269]}, {"componentType": 5126, "type": "VEC3", "count": 3953, "bufferView": 12, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 3953, "bufferView": 13, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 3953, "bufferView": 14, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 9960, "bufferView": 15, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 6454, "bufferView": 16, "byteOffset": 0, "min": [0.861436426639557, -0.306409955024719, -0.304731279611588], "max": [1.24471998214722, 0.306409448385239, 0.304731070995331]}, {"componentType": 5126, "type": "VEC3", "count": 6454, "bufferView": 17, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 6454, "bufferView": 18, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 6454, "bufferView": 19, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 9960, "bufferView": 20, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 6384, "bufferView": 21, "byteOffset": 0, "min": [-1.24471986293793, -0.306409329175949, -0.304730713367462], "max": [-0.861436188220978, 0.306410104036331, 0.304731667041779]}, {"componentType": 5126, "type": "VEC3", "count": 6384, "bufferView": 22, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 6384, "bufferView": 23, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 6384, "bufferView": 24, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 6750, "bufferView": 25, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 3616, "bufferView": 26, "byteOffset": 0, "min": [-1.27348220348358, -0.185038834810257, -0.184025079011917], "max": [-1.24439382553101, 0.185039788484573, 0.184026181697845]}, {"componentType": 5126, "type": "VEC3", "count": 3616, "bufferView": 27, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 3616, "bufferView": 28, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 3616, "bufferView": 29, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 2430, "bufferView": 30, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 1424, "bufferView": 31, "byteOffset": 0, "min": [0.938671112060547, -0.111930251121521, -0.111316986382008], "max": [1.12691605091095, 0.11192986369133, 0.111316792666912]}, {"componentType": 5126, "type": "VEC3", "count": 1424, "bufferView": 32, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1424, "bufferView": 33, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1424, "bufferView": 34, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 21036, "bufferView": 35, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 13638, "bufferView": 36, "byteOffset": 0, "min": [-1.24993395805359, -0.306409388780594, -0.304730772972107], "max": [-0.455958545207977, 0.306410133838654, 0.304731696844101]}, {"componentType": 5126, "type": "VEC3", "count": 13638, "bufferView": 37, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 13638, "bufferView": 38, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 13638, "bufferView": 39, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 2430, "bufferView": 40, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 1462, "bufferView": 41, "byteOffset": 0, "min": [-1.12691617012024, -0.111929662525654, -0.111316449940205], "max": [-0.938671112060547, 0.111930452287197, 0.111317329108715]}, {"componentType": 5126, "type": "VEC3", "count": 1462, "bufferView": 42, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1462, "bufferView": 43, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1462, "bufferView": 44, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 17412, "bufferView": 45, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 16810, "bufferView": 46, "byteOffset": 0, "min": [-1.29637813568115, 3.64125108718872, -0.587888598442078], "max": [1.29638779163361, 11.6468553543091, 1.56117117404938]}, {"componentType": 5126, "type": "VEC3", "count": 16810, "bufferView": 47, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 16810, "bufferView": 48, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 16810, "bufferView": 49, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 9216, "bufferView": 50, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 6160, "bufferView": 51, "byteOffset": 0, "min": [-1.81774747371674, -0.131882011890411, 0.000825393537525088], "max": [0.131881982088089, 7.52886867523193, 0.257990062236786]}, {"componentType": 5126, "type": "VEC3", "count": 6160, "bufferView": 52, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 6160, "bufferView": 53, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 6160, "bufferView": 54, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 540, "bufferView": 55, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 360, "bufferView": 56, "byteOffset": 0, "min": [0.89350563287735, -0.535317778587341, -0.532385110855103], "max": [1.19848144054413, 0.535317301750183, 0.532384872436523]}, {"componentType": 5126, "type": "VEC3", "count": 360, "bufferView": 57, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 360, "bufferView": 58, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 360, "bufferView": 59, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 540, "bufferView": 60, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 360, "bufferView": 61, "byteOffset": 0, "min": [-1.19848144054413, -0.535317122936249, -0.532384514808655], "max": [-0.893505454063416, 0.535317957401276, 0.532385468482971]}, {"componentType": 5126, "type": "VEC3", "count": 360, "bufferView": 62, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 360, "bufferView": 63, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 360, "bufferView": 64, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 624, "bufferView": 65, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 416, "bufferView": 66, "byteOffset": 0, "min": [0.982519686222076, -0.534146428108215, -0.531220257282257], "max": [1.2250828742981, 0.534145951271057, 0.531219899654388]}, {"componentType": 5126, "type": "VEC3", "count": 416, "bufferView": 67, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 416, "bufferView": 68, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 416, "bufferView": 69, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 1080, "bufferView": 70, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 720, "bufferView": 71, "byteOffset": 0, "min": [-1.2178647518158, -0.534145891666412, -0.531219720840454], "max": [-0.660510063171387, 0.534146666526794, 0.531220555305481]}, {"componentType": 5126, "type": "VEC3", "count": 720, "bufferView": 72, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 720, "bufferView": 73, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 720, "bufferView": 74, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 59124, "bufferView": 75, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 54910, "bufferView": 76, "byteOffset": 0, "min": [-1.27210640907288, -1.53801786899567, -1.23147690296173], "max": [1.27211046218872, 6.40596055984497, 1.74020135402679]}, {"componentType": 5126, "type": "VEC3", "count": 54910, "bufferView": 77, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 54910, "bufferView": 78, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 54910, "bufferView": 79, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 3528, "bufferView": 80, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 2360, "bufferView": 81, "byteOffset": 0, "min": [-1.09450078010559, -1.50901460647583, -0.832514464855194], "max": [1.09449946880341, -1.38691842556, -0.652198195457458]}, {"componentType": 5126, "type": "VEC3", "count": 2360, "bufferView": 82, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 2360, "bufferView": 83, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 2360, "bufferView": 84, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 5520, "bufferView": 85, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 5464, "bufferView": 86, "byteOffset": 0, "min": [-1.19191026687622, -1.52951550483704, -0.782126128673553], "max": [1.19191074371338, 1.18313753604889, 1.27835440635681]}, {"componentType": 5126, "type": "VEC3", "count": 5464, "bufferView": 87, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 5464, "bufferView": 88, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 5464, "bufferView": 89, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 14856, "bufferView": 90, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 14826, "bufferView": 91, "byteOffset": 0, "min": [-1.30043840408325, 3.63857626914978, -0.464797377586365], "max": [1.30045652389526, 11.6497392654419, 1.56114375591278]}, {"componentType": 5126, "type": "VEC3", "count": 14826, "bufferView": 92, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 14826, "bufferView": 93, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 14826, "bufferView": 94, "byteOffset": 0}, {"componentType": 5125, "type": "SCALAR", "count": 122271, "bufferView": 95, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 102959, "bufferView": 96, "byteOffset": 0, "min": [-1.47413110733032, -1.41773951053619, -0.373956561088562], "max": [1.47413086891174, 6.57123041152954, 2.64255619049072]}, {"componentType": 5126, "type": "VEC3", "count": 102959, "bufferView": 97, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 102959, "bufferView": 98, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 102959, "bufferView": 99, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 31770, "bufferView": 100, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 29531, "bufferView": 101, "byteOffset": 0, "min": [-1.23950338363647, -4.96153974533081, -2.05105590820312], "max": [1.12786209583282, 0.365691900253296, 0.935589730739594]}, {"componentType": 5126, "type": "VEC3", "count": 29531, "bufferView": 102, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 29531, "bufferView": 103, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 29531, "bufferView": 104, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 8478, "bufferView": 105, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 5662, "bufferView": 106, "byteOffset": 0, "min": [-1.23850440979004, -1.57296824455261, -1.21618831157684], "max": [1.18963003158569, 1.53044414520264, 1.08008790016174]}, {"componentType": 5126, "type": "VEC3", "count": 5662, "bufferView": 107, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 5662, "bufferView": 108, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 5662, "bufferView": 109, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 72, "bufferView": 110, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 48, "bufferView": 111, "byteOffset": 0, "min": [-0.387806117534637, -1.39175367355347, -0.0527617186307907], "max": [0.387805283069611, -1.35079908370972, 0.685405194759369]}, {"componentType": 5126, "type": "VEC3", "count": 48, "bufferView": 112, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 48, "bufferView": 113, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 48, "bufferView": 114, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 8157, "bufferView": 115, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 5379, "bufferView": 116, "byteOffset": 0, "min": [-1.08560657501221, -1.49409937858582, -1.03704369068146], "max": [1.08560526371002, 6.33126258850098, 1.39125299453735]}, {"componentType": 5126, "type": "VEC3", "count": 5379, "bufferView": 117, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 5379, "bufferView": 118, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 5379, "bufferView": 119, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 1740, "bufferView": 120, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 1572, "bufferView": 121, "byteOffset": 0, "min": [1.24432969093323, -0.170168191194534, -0.162207186222076], "max": [1.2719419002533, 0.17002360522747, 0.162424311041832]}, {"componentType": 5126, "type": "VEC3", "count": 1572, "bufferView": 122, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1572, "bufferView": 123, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1572, "bufferView": 124, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 372, "bufferView": 125, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 248, "bufferView": 126, "byteOffset": 0, "min": [-1.12039983272552, -1.41531312465668, 1.00711703300476], "max": [1.12039840221405, -1.36482620239258, 1.03776299953461]}, {"componentType": 5126, "type": "VEC3", "count": 248, "bufferView": 127, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 248, "bufferView": 128, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 248, "bufferView": 129, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 4143, "bufferView": 130, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 4101, "bufferView": 131, "byteOffset": 0, "min": [-1.46710169315338, -1.38324058055878, 0.108787588775158], "max": [1.4671014547348, -0.825402140617371, 1.52763032913208]}, {"componentType": 5126, "type": "VEC3", "count": 4101, "bufferView": 132, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 4101, "bufferView": 133, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 4101, "bufferView": 134, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 3048, "bufferView": 135, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 2042, "bufferView": 136, "byteOffset": 0, "min": [-0.328704595565796, -0.00157997489441186, -0.0532530061900616], "max": [0.328704565763474, 0.00618869485333562, 0.0531773380935192]}, {"componentType": 5126, "type": "VEC3", "count": 2042, "bufferView": 137, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 2042, "bufferView": 138, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 2042, "bufferView": 139, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 2880, "bufferView": 140, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 1920, "bufferView": 141, "byteOffset": 0, "min": [0.854665577411652, -0.525850415229797, -0.522969663143158], "max": [1.23732149600983, 0.525849938392639, 0.522969424724579]}, {"componentType": 5126, "type": "VEC3", "count": 1920, "bufferView": 142, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1920, "bufferView": 143, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1920, "bufferView": 144, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 2880, "bufferView": 145, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 1920, "bufferView": 146, "byteOffset": 0, "min": [-1.23732161521912, -0.525849759578705, -0.52296906709671], "max": [-0.854665279388428, 0.525850594043732, 0.522970020771027]}, {"componentType": 5126, "type": "VEC3", "count": 1920, "bufferView": 147, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1920, "bufferView": 148, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1920, "bufferView": 149, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 1740, "bufferView": 150, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 1562, "bufferView": 151, "byteOffset": 0, "min": [-1.2719419002533, -0.170167401432991, -0.162206470966339], "max": [-1.24432969093323, 0.170024394989014, 0.162425011396408]}, {"componentType": 5126, "type": "VEC3", "count": 1562, "bufferView": 152, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1562, "bufferView": 153, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1562, "bufferView": 154, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 6216, "bufferView": 155, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 4144, "bufferView": 156, "byteOffset": 0, "min": [0.621670305728912, -0.534146308898926, -0.531220197677612], "max": [1.25670480728149, 0.534146130084991, 0.531220078468323]}, {"componentType": 5126, "type": "VEC3", "count": 4144, "bufferView": 157, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 4144, "bufferView": 158, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 4144, "bufferView": 159, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 5760, "bufferView": 160, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 3840, "bufferView": 161, "byteOffset": 0, "min": [-1.25670480728149, -0.526347160339355, -0.523463666439056], "max": [-0.621670186519623, 0.526347935199738, 0.523464560508728]}, {"componentType": 5126, "type": "VEC3", "count": 3840, "bufferView": 162, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 3840, "bufferView": 163, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 3840, "bufferView": 164, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 2220, "bufferView": 165, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 1958, "bufferView": 166, "byteOffset": 0, "min": [0.941789507865906, -0.160439848899841, -0.153307601809502], "max": [1.11442744731903, 0.161087274551392, 0.153512954711914]}, {"componentType": 5126, "type": "VEC3", "count": 1958, "bufferView": 167, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1958, "bufferView": 168, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1958, "bufferView": 169, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 2220, "bufferView": 170, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 1902, "bufferView": 171, "byteOffset": 0, "min": [-1.11442744731903, -0.160439252853394, -0.153307065367699], "max": [-0.941789507865906, 0.161087870597839, 0.153513491153717]}, {"componentType": 5126, "type": "VEC3", "count": 1902, "bufferView": 172, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1902, "bufferView": 173, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1902, "bufferView": 174, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 2760, "bufferView": 175, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 1848, "bufferView": 176, "byteOffset": 0, "min": [-1.09113025665283, -5.67315006256104, 0.0821342542767525], "max": [0.261553376913071, 2.31165289878845, 0.207226753234863]}, {"componentType": 5126, "type": "VEC3", "count": 1848, "bufferView": 177, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1848, "bufferView": 178, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1848, "bufferView": 179, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 8640, "bufferView": 180, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 8640, "bufferView": 181, "byteOffset": 0, "min": [-1.20161128044128, 11.5475692749023, -0.579354822635651], "max": [1.20163321495056, 11.5561714172363, -0.0250085722655058]}, {"componentType": 5126, "type": "VEC3", "count": 8640, "bufferView": 182, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 8640, "bufferView": 183, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 8640, "bufferView": 184, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 6216, "bufferView": 185, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 4132, "bufferView": 186, "byteOffset": 0, "min": [-1.2091863155365, -1.42542517185211, -0.83265632390976], "max": [1.2091851234436, -1.21623432636261, -0.652005970478058]}, {"componentType": 5126, "type": "VEC3", "count": 4132, "bufferView": 187, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 4132, "bufferView": 188, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 4132, "bufferView": 189, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 2880, "bufferView": 190, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 2880, "bufferView": 191, "byteOffset": 0, "min": [-1.20161128044128, 11.5475692749023, -0.235843822360039], "max": [1.20163321495056, 11.5561714172363, -0.140279352664948]}, {"componentType": 5126, "type": "VEC3", "count": 2880, "bufferView": 192, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 2880, "bufferView": 193, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 2880, "bufferView": 194, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 636, "bufferView": 195, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 412, "bufferView": 196, "byteOffset": 0, "min": [-1.1950089931488, -0.811076402664185, -0.0181241147220135], "max": [1.19500851631165, -0.71288138628006, 0.0267531890422106]}, {"componentType": 5126, "type": "VEC3", "count": 412, "bufferView": 197, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 412, "bufferView": 198, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 412, "bufferView": 199, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 3936, "bufferView": 200, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 2248, "bufferView": 201, "byteOffset": 0, "min": [-1.22360968589783, -5.14983558654785, 0.0778004601597786], "max": [1.22360670566559, 2.43516635894775, 2.6085033416748]}, {"componentType": 5126, "type": "VEC3", "count": 2248, "bufferView": 202, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 2248, "bufferView": 203, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 2248, "bufferView": 204, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 2400, "bufferView": 205, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 1600, "bufferView": 206, "byteOffset": 0, "min": [-0.335043638944626, -1.450404047966, -0.0991891622543335], "max": [0.336225539445877, -0.0469326302409172, 0.249482333660126]}, {"componentType": 5126, "type": "VEC3", "count": 1600, "bufferView": 207, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1600, "bufferView": 208, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 1600, "bufferView": 209, "byteOffset": 0}, {"componentType": 5123, "type": "SCALAR", "count": 27354, "bufferView": 210, "byteOffset": 0}, {"componentType": 5126, "type": "VEC3", "count": 22307, "bufferView": 211, "byteOffset": 0, "min": [-1.6481614112854, -0.215552121400833, -2.61571621894836], "max": [0.605964839458466, 6.0372486114502, 0.0549999326467514]}, {"componentType": 5126, "type": "VEC3", "count": 22307, "bufferView": 212, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 22307, "bufferView": 213, "byteOffset": 0}, {"componentType": 5126, "type": "VEC2", "count": 22307, "bufferView": 214, "byteOffset": 0}], "images": [{"name": "AM98_005_fire_eu_metal_floor.png", "uri": "AM98_005_fire_eu_metal_floor.png"}, {"name": "AM98_005_fire_eu_light_white.png", "uri": "AM98_005_fire_eu_light_white.png"}, {"name": "AM98_005_fire_eu_light_orange.png", "uri": "AM98_005_fire_eu_light_orange.png"}, {"name": "AM98_005_fire_eu_tire.png", "uri": "AM98_005_fire_eu_tire.png"}], "samplers": [{}], "textures": [{"name": "AM98_005_fire_eu_metal_floor", "sampler": 0, "source": 0}, {"name": "AM98_005_fire_eu_light_white", "sampler": 0, "source": 1}, {"name": "AM98_005_fire_eu_light_orange", "sampler": 0, "source": 2}, {"name": "AM98_005_fire_eu_tire", "sampler": 0, "source": 3}], "materials": [{"name": "材质.1", "alphaMode": "OPAQUE", "extras": {"fromFBX": {"shadingModel": "<PERSON>", "isTruePBR": false}}, "pbrMetallicRoughness": {"baseColorFactor": [0.800000011920929, 0.151999995112419, 0.151999995112419, 1.0], "metallicFactor": 0.200000002980232, "roughnessFactor": 0.800000011920929}}, {"name": "材质.4", "alphaMode": "OPAQUE", "extras": {"fromFBX": {"shadingModel": "<PERSON><PERSON>", "isTruePBR": false}}, "pbrMetallicRoughness": {"baseColorFactor": [0.800000011920929, 0.800000011920929, 0.800000011920929, 0.0], "metallicFactor": 0.400000005960464, "roughnessFactor": 0.242535620927811}}, {"name": "材质.5", "alphaMode": "OPAQUE", "extras": {"fromFBX": {"shadingModel": "<PERSON><PERSON>", "isTruePBR": false}}, "pbrMetallicRoughness": {"baseColorFactor": [0.170000001788139, 0.170000001788139, 0.170000001788139, 1.0], "metallicFactor": 0.400000005960464, "roughnessFactor": 0.242535620927811}}, {"name": "DefaultMaterial", "alphaMode": "OPAQUE", "extras": {"fromFBX": {"shadingModel": "<PERSON>", "isTruePBR": false}}, "pbrMetallicRoughness": {"baseColorFactor": [0.5, 0.5, 0.5, 1.0], "metallicFactor": 0.200000002980232, "roughnessFactor": 0.800000011920929}}, {"name": "材质", "alphaMode": "OPAQUE", "extras": {"fromFBX": {"shadingModel": "<PERSON><PERSON>", "isTruePBR": false}}, "pbrMetallicRoughness": {"baseColorTexture": {"index": 0, "texCoord": 0}, "baseColorFactor": [1.0, 1.0, 1.0, 1.0], "metallicFactor": 0.400000005960464, "roughnessFactor": 0.242535620927811}}, {"name": "材质.5", "alphaMode": "OPAQUE", "extras": {"fromFBX": {"shadingModel": "<PERSON><PERSON>", "isTruePBR": false}}, "pbrMetallicRoughness": {"baseColorFactor": [0.310000002384186, 0.310000002384186, 0.310000002384186, 1.0], "metallicFactor": 0.400000005960464, "roughnessFactor": 0.242535620927811}}, {"name": "材质.2", "alphaMode": "OPAQUE", "extras": {"fromFBX": {"shadingModel": "<PERSON><PERSON>", "isTruePBR": false}}, "pbrMetallicRoughness": {"baseColorTexture": {"index": 1, "texCoord": 0}, "baseColorFactor": [1.0, 1.0, 1.0, 1.0], "metallicFactor": 0.400000005960464, "roughnessFactor": 0.242535620927811}}, {"name": "材质.7", "alphaMode": "OPAQUE", "extras": {"fromFBX": {"shadingModel": "<PERSON><PERSON>", "isTruePBR": false}}, "pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 1.0], "metallicFactor": 0.400000005960464, "roughnessFactor": 0.111948154866695}}, {"name": "材质", "alphaMode": "OPAQUE", "extras": {"fromFBX": {"shadingModel": "<PERSON><PERSON>", "isTruePBR": false}}, "pbrMetallicRoughness": {"baseColorTexture": {"index": 2, "texCoord": 0}, "baseColorFactor": [1.0, 1.0, 1.0, 0.0], "metallicFactor": 0.400000005960464, "roughnessFactor": 0.242535620927811}}, {"name": "材质.3", "alphaMode": "OPAQUE", "extras": {"fromFBX": {"shadingModel": "<PERSON><PERSON>", "isTruePBR": false}}, "pbrMetallicRoughness": {"baseColorTexture": {"index": 3, "texCoord": 0}, "baseColorFactor": [1.0, 1.0, 1.0, 1.0], "metallicFactor": 0.400000005960464, "roughnessFactor": 0.242535620927811}}], "meshes": [{"name": "AM98_005_fire_eu_blinds", "primitives": [{"material": 4, "mode": 4, "attributes": {"NORMAL": 2, "POSITION": 1, "TEXCOORD_0": 3, "TEXCOORD_1": 4}, "indices": 0}]}, {"name": "AM98_005_fire_eu_rim_rl", "primitives": [{"material": 6, "mode": 4, "attributes": {"NORMAL": 7, "POSITION": 6, "TEXCOORD_0": 8, "TEXCOORD_1": 9}, "indices": 5}]}, {"name": "AM98_005_fire_eu_rim_cap_fl", "primitives": [{"material": 6, "mode": 4, "attributes": {"NORMAL": 12, "POSITION": 11, "TEXCOORD_0": 13, "TEXCOORD_1": 14}, "indices": 10}]}, {"name": "AM98_005_fire_eu_rim_fl", "primitives": [{"material": 6, "mode": 4, "attributes": {"NORMAL": 17, "POSITION": 16, "TEXCOORD_0": 18, "TEXCOORD_1": 19}, "indices": 15}]}, {"name": "AM98_005_fire_eu_rim_fr", "primitives": [{"material": 6, "mode": 4, "attributes": {"NORMAL": 22, "POSITION": 21, "TEXCOORD_0": 23, "TEXCOORD_1": 24}, "indices": 20}]}, {"name": "AM98_005_fire_eu_rim_cap_fr", "primitives": [{"material": 6, "mode": 4, "attributes": {"NORMAL": 27, "POSITION": 26, "TEXCOORD_0": 28, "TEXCOORD_1": 29}, "indices": 25}]}, {"name": "AM98_005_fire_eu_rim_cap_rl", "primitives": [{"material": 6, "mode": 4, "attributes": {"NORMAL": 32, "POSITION": 31, "TEXCOORD_0": 33, "TEXCOORD_1": 34}, "indices": 30}]}, {"name": "AM98_005_fire_eu_rim_rr", "primitives": [{"material": 6, "mode": 4, "attributes": {"NORMAL": 37, "POSITION": 36, "TEXCOORD_0": 38, "TEXCOORD_1": 39}, "indices": 35}]}, {"name": "AM98_005_fire_eu_rim_cap_rr", "primitives": [{"material": 6, "mode": 4, "attributes": {"NORMAL": 42, "POSITION": 41, "TEXCOORD_0": 43, "TEXCOORD_1": 44}, "indices": 40}]}, {"name": "AM98_005_fire_eu_chrome_squares", "primitives": [{"material": 8, "mode": 4, "attributes": {"NORMAL": 47, "POSITION": 46, "TEXCOORD_0": 48, "TEXCOORD_1": 49}, "indices": 45}]}, {"name": "AM98_005_fire_eu_blue_glass", "primitives": [{"material": 8, "mode": 4, "attributes": {"NORMAL": 52, "POSITION": 51, "TEXCOORD_0": 53, "TEXCOORD_1": 54}, "indices": 50}]}, {"name": "AM98_005_fire_eu_tire_fl", "primitives": [{"material": 9, "mode": 4, "attributes": {"NORMAL": 57, "POSITION": 56, "TEXCOORD_0": 58, "TEXCOORD_1": 59}, "indices": 55}, {"material": 3, "mode": 4, "attributes": {"NORMAL": 142, "POSITION": 141, "TEXCOORD_0": 143, "TEXCOORD_1": 144}, "indices": 140}]}, {"name": "AM98_005_fire_eu_tire_fr", "primitives": [{"material": 9, "mode": 4, "attributes": {"NORMAL": 62, "POSITION": 61, "TEXCOORD_0": 63, "TEXCOORD_1": 64}, "indices": 60}, {"material": 3, "mode": 4, "attributes": {"NORMAL": 147, "POSITION": 146, "TEXCOORD_0": 148, "TEXCOORD_1": 149}, "indices": 145}]}, {"name": "AM98_005_fire_eu_tire_rl", "primitives": [{"material": 9, "mode": 4, "attributes": {"NORMAL": 67, "POSITION": 66, "TEXCOORD_0": 68, "TEXCOORD_1": 69}, "indices": 65}, {"material": 3, "mode": 4, "attributes": {"NORMAL": 157, "POSITION": 156, "TEXCOORD_0": 158, "TEXCOORD_1": 159}, "indices": 155}]}, {"name": "AM98_005_fire_eu_tire_rr", "primitives": [{"material": 9, "mode": 4, "attributes": {"NORMAL": 72, "POSITION": 71, "TEXCOORD_0": 73, "TEXCOORD_1": 74}, "indices": 70}, {"material": 3, "mode": 4, "attributes": {"NORMAL": 162, "POSITION": 161, "TEXCOORD_0": 163, "TEXCOORD_1": 164}, "indices": 160}]}, {"name": "AM98_005_fire_eu_carpaint", "primitives": [{"material": 0, "mode": 4, "attributes": {"NORMAL": 77, "POSITION": 76, "TEXCOORD_0": 78, "TEXCOORD_1": 79}, "indices": 75}]}, {"name": "AM98_005_fire_eu_headlights_glass", "primitives": [{"material": 1, "mode": 4, "attributes": {"NORMAL": 82, "POSITION": 81, "TEXCOORD_0": 83, "TEXCOORD_1": 84}, "indices": 80}]}, {"name": "AM98_005_fire_eu_glass", "primitives": [{"material": 1, "mode": 4, "attributes": {"NORMAL": 87, "POSITION": 86, "TEXCOORD_0": 88, "TEXCOORD_1": 89}, "indices": 85}]}, {"name": "AM98_005_fire_eu_white_light", "primitives": [{"material": 1, "mode": 4, "attributes": {"NORMAL": 92, "POSITION": 91, "TEXCOORD_0": 93, "TEXCOORD_1": 94}, "indices": 90}]}, {"name": "AM98_005_fire_eu_black_plastic", "primitives": [{"material": 2, "mode": 4, "attributes": {"NORMAL": 97, "POSITION": 96, "TEXCOORD_0": 98, "TEXCOORD_1": 99}, "indices": 95}]}, {"name": "AM98_005_fire_eu_silver", "primitives": [{"material": 2, "mode": 4, "attributes": {"NORMAL": 102, "POSITION": 101, "TEXCOORD_0": 103, "TEXCOORD_1": 104}, "indices": 100}]}, {"name": "AM98_005_fire_eu_black_shiny", "primitives": [{"material": 2, "mode": 4, "attributes": {"NORMAL": 107, "POSITION": 106, "TEXCOORD_0": 108, "TEXCOORD_1": 109}, "indices": 105}]}, {"name": "AM98_005_fire_eu_grill", "primitives": [{"material": 2, "mode": 4, "attributes": {"NORMAL": 112, "POSITION": 111, "TEXCOORD_0": 113, "TEXCOORD_1": 114}, "indices": 110}]}, {"name": "AM98_005_fire_eu_chrome_stripes", "primitives": [{"material": 3, "mode": 4, "attributes": {"NORMAL": 117, "POSITION": 116, "TEXCOORD_0": 118, "TEXCOORD_1": 119}, "indices": 115}]}, {"name": "AM98_005_fire_eu_bolts_fl", "primitives": [{"material": 3, "mode": 4, "attributes": {"NORMAL": 122, "POSITION": 121, "TEXCOORD_0": 123, "TEXCOORD_1": 124}, "indices": 120}]}, {"name": "AM98_005_fire_eu_marker_lights", "primitives": [{"material": 3, "mode": 4, "attributes": {"NORMAL": 127, "POSITION": 126, "TEXCOORD_0": 128, "TEXCOORD_1": 129}, "indices": 125}]}, {"name": "AM98_005_fire_eu_chrome", "primitives": [{"material": 3, "mode": 4, "attributes": {"NORMAL": 132, "POSITION": 131, "TEXCOORD_0": 133, "TEXCOORD_1": 134}, "indices": 130}]}, {"name": "AM98_005_fire_eu_scania_logo", "primitives": [{"material": 3, "mode": 4, "attributes": {"NORMAL": 137, "POSITION": 136, "TEXCOORD_0": 138, "TEXCOORD_1": 139}, "indices": 135}]}, {"name": "AM98_005_fire_eu_bolts_fr", "primitives": [{"material": 3, "mode": 4, "attributes": {"NORMAL": 152, "POSITION": 151, "TEXCOORD_0": 153, "TEXCOORD_1": 154}, "indices": 150}]}, {"name": "AM98_005_fire_eu_bolts_rl", "primitives": [{"material": 3, "mode": 4, "attributes": {"NORMAL": 167, "POSITION": 166, "TEXCOORD_0": 168, "TEXCOORD_1": 169}, "indices": 165}]}, {"name": "AM98_005_fire_eu_bolts_rr", "primitives": [{"material": 3, "mode": 4, "attributes": {"NORMAL": 172, "POSITION": 171, "TEXCOORD_0": 173, "TEXCOORD_1": 174}, "indices": 170}]}, {"name": "AM98_005_fire_eu_register_plate", "primitives": [{"material": 3, "mode": 4, "attributes": {"NORMAL": 177, "POSITION": 176, "TEXCOORD_0": 178, "TEXCOORD_1": 179}, "indices": 175}]}, {"name": "AM98_005_fire_eu_red_light", "primitives": [{"material": 3, "mode": 4, "attributes": {"NORMAL": 182, "POSITION": 181, "TEXCOORD_0": 183, "TEXCOORD_1": 184}, "indices": 180}]}, {"name": "AM98_005_fire_eu_blinkers", "primitives": [{"material": 3, "mode": 4, "attributes": {"NORMAL": 187, "POSITION": 186, "TEXCOORD_0": 188, "TEXCOORD_1": 189}, "indices": 185}]}, {"name": "AM98_005_fire_eu_orange_light", "primitives": [{"material": 3, "mode": 4, "attributes": {"NORMAL": 192, "POSITION": 191, "TEXCOORD_0": 193, "TEXCOORD_1": 194}, "indices": 190}]}, {"name": "AM98_005_fire_eu_orange", "primitives": [{"material": 3, "mode": 4, "attributes": {"NORMAL": 197, "POSITION": 196, "TEXCOORD_0": 198, "TEXCOORD_1": 199}, "indices": 195}]}, {"name": "AM98_005_fire_eu_metal", "primitives": [{"material": 5, "mode": 4, "attributes": {"NORMAL": 202, "POSITION": 201, "TEXCOORD_0": 203, "TEXCOORD_1": 204}, "indices": 200}]}, {"name": "AM98_005_fire_eu_rubber", "primitives": [{"material": 5, "mode": 4, "attributes": {"NORMAL": 207, "POSITION": 206, "TEXCOORD_0": 208, "TEXCOORD_1": 209}, "indices": 205}]}, {"name": "AM98_005_fire_eu_ladder", "primitives": [{"material": 7, "mode": 4, "attributes": {"NORMAL": 212, "POSITION": 211, "TEXCOORD_0": 213, "TEXCOORD_1": 214}, "indices": 210}]}], "cameras": [{"name": "", "type": "perspective", "perspective": {"znear": 0.00100000004749745, "zfar": 10000.0, "aspectRatio": 1.77777779102325, "yfov": 0.548334896564484}}], "nodes": [{"name": "RootNode", "translation": [0.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "children": [1, 2, 3, 12]}, {"name": "Cinema_4D_编辑器", "translation": [9.82544612884521, 3.11207461357117, 13.6267700195312], "rotation": [-0.0276720747351646, 0.328165590763092, 0.00961803738027811, 0.944165706634521], "scale": [1.0, 1.0, 1.0], "camera": 0}, {"name": "灯光", "translation": [6.76096057891846, 9.66373348236084, 5.27219724655151], "rotation": [0.70710676908493, 0.0, 0.0, 0.70710676908493], "scale": [1.0, 1.0, 1.0], "extensions": {"KHR_lights_punctual": {"light": 0}}}, {"name": "物理天空", "translation": [0.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "children": [4, 10, 11]}, {"name": "空白天空", "translation": [9.82544612884521, 3.11207461357117, 13.6267700195312], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "children": [5, 8]}, {"name": "太阳", "translation": [86.9500198364258, 784.982299804688, 1254.95922851562], "rotation": [-0.275174885988235, 0.0332437418401241, 0.00952135678380728, 0.960772097110748], "scale": [1.0, 1.0, 1.0], "children": [6, 7]}, {"name": "镜头光晕源", "translation": [0.0, 0.0, 0.0], "rotation": [8.32667268468867e-17, -1.73472347597681e-17, 6.93889390390723e-18, 1.0], "scale": [1.0, 1.0, 1.0], "extensions": {"KHR_lights_punctual": {"light": 0}}}, {"name": "阳光", "translation": [0.0, 0.0, 0.0], "rotation": [8.32667268468867e-17, -1.73472347597681e-17, 6.93889390390723e-18, 1.0], "scale": [1.0, 1.0, 1.0], "extensions": {"KHR_lights_punctual": {"light": 1}}}, {"name": "月亮", "translation": [2.27526473999023, 17.8720874786377, 32.9519004821777], "rotation": [-0.245305076241493, 0.0334082320332527, 0.00845885556191206, 0.968833208084106], "scale": [1.0, 1.0, 1.0], "children": [9]}, {"name": "月光", "translation": [0.0, 0.0, 0.0], "rotation": [1.20370621524202e-35, -3.46944695195361e-18, 3.46944695195361e-18, 1.0], "scale": [1.0, 1.0, 1.0], "extensions": {"KHR_lights_punctual": {"light": 1}}}, {"name": "投影平面", "translation": [0.0, 100.0, 0.0], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0]}, {"name": "Sky_Dome", "translation": [0.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0]}, {"name": "AM98_005_fire_eu", "translation": [0.0, 1.70862650871277, 0.0], "rotation": [-0.70710676908493, -0.0, 0.0, 0.70710676908493], "scale": [1.0, 1.0, 1.0], "children": [13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51]}, {"name": "AM98_005_fire_eu_carpaint", "translation": [2.36055484492681e-07, -2.52182269096375, -0.0652636736631393], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 15}, {"name": "AM98_005_fire_eu_headlights_glass", "translation": [2.36055484492681e-07, -2.52182269096375, -0.0652636736631393], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 16}, {"name": "AM98_005_fire_eu_black_plastic", "translation": [2.05025997956909e-07, -2.64196157455444, -0.915250241756439], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 19}, {"name": "AM98_005_fire_eu_glass", "translation": [2.37426490912185e-07, -2.51728272438049, -0.331393420696259], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 17}, {"name": "AM98_005_fire_eu_chrome_stripes", "translation": [2.36055484492681e-07, -2.52182269096375, -0.0652636736631393], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 23}, {"name": "AM98_005_fire_eu_blinds", "translation": [3.41005062409749e-07, -2.11548256874084, -1.6332631111145], "rotation": [1.11022302462516e-16, 2.76424087155334e-23, 8.68879510562692e-08, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 0}, {"name": "AM98_005_fire_eu_metal", "translation": [1.41820703447593e-06, 1.45151078701019, -1.29674065113068], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 36}, {"name": "AM98_005_fire_eu_rim_rl", "translation": [1.48292360790947e-06, 1.66581010818481, -1.19498467445374], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 1}, {"name": "AM98_005_fire_eu_bolts_fl", "translation": [1.94680751519627e-07, -2.600013256073, -1.19492077827454], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 24}, {"name": "AM98_005_fire_eu_ladder", "translation": [0.582099199295044, -1.97774624824524, 1.43595588207245], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 38}, {"name": "AM98_005_fire_eu_silver", "translation": [2.36226401284512e-06, 3.73132491111755, 0.768612682819366], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 20}, {"name": "AM98_005_fire_eu_chrome_squares", "translation": [-5.5890795920277e-06, -7.63842391967773, -0.383663326501846], "rotation": [1.11022302462516e-16, 2.76424087155334e-23, 8.68879510562692e-08, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 9}, {"name": "AM98_005_fire_eu_blue_glass", "translation": [0.84336131811142, -3.68033313751221, 1.09418046474457], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 10}, {"name": "AM98_005_fire_eu_marker_lights", "translation": [2.36055484492681e-07, -2.52182269096375, -0.0652636736631393], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 25}, {"name": "AM98_005_fire_eu_chrome", "translation": [2.05025997956909e-07, -2.64196157455444, -0.915250241756439], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 26}, {"name": "AM98_005_fire_eu_scania_logo", "translation": [0.000731660984456539, -4.05895185470581, -0.0617599487304688], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 27}, {"name": "AM98_005_fire_eu_tire_fl", "translation": [1.94680751519627e-07, -2.600013256073, -1.19492077827454], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 11}, {"name": "AM98_005_fire_eu_tire_fr", "translation": [1.94680751519627e-07, -2.600013256073, -1.19492077827454], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 12}, {"name": "AM98_005_fire_eu_rim_cap_fl", "translation": [1.94680751519627e-07, -2.600013256073, -1.19492077827454], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 2}, {"name": "AM98_005_fire_eu_rim_fl", "translation": [1.94680751519627e-07, -2.600013256073, -1.19492077827454], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 3}, {"name": "AM98_005_fire_eu_rim_fr", "translation": [1.94680751519627e-07, -2.600013256073, -1.19492077827454], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 4}, {"name": "AM98_005_fire_eu_rim_cap_fr", "translation": [1.94680751519627e-07, -2.600013256073, -1.19492077827454], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 5}, {"name": "AM98_005_fire_eu_bolts_fr", "translation": [1.94680751519627e-07, -2.600013256073, -1.19492077827454], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 28}, {"name": "AM98_005_fire_eu_tire_rl", "translation": [1.48292360790947e-06, 1.66581010818481, -1.19498467445374], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 13}, {"name": "AM98_005_fire_eu_tire_rr", "translation": [1.48292360790947e-06, 1.66581010818481, -1.19498467445374], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 14}, {"name": "AM98_005_fire_eu_rim_cap_rl", "translation": [1.48292360790947e-06, 1.66581010818481, -1.19498467445374], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 6}, {"name": "AM98_005_fire_eu_bolts_rl", "translation": [1.48292360790947e-06, 1.66581010818481, -1.19498467445374], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 29}, {"name": "AM98_005_fire_eu_rim_rr", "translation": [1.48292360790947e-06, 1.66581010818481, -1.19498467445374], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 7}, {"name": "AM98_005_fire_eu_rim_cap_rr", "translation": [1.48292360790947e-06, 1.66581010818481, -1.19498467445374], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 8}, {"name": "AM98_005_fire_eu_bolts_rr", "translation": [1.48292360790947e-06, 1.66581010818481, -1.19498467445374], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 30}, {"name": "AM98_005_fire_eu_register_plate", "translation": [1.43880220093706e-06, 1.57613289356232, -1.20260906219482], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 31}, {"name": "AM98_005_fire_eu_white_light", "translation": [-5.5890795920277e-06, -7.63842391967773, -0.383663326501846], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 18}, {"name": "AM98_005_fire_eu_red_light", "translation": [-5.5890795920277e-06, -7.63842391967773, -0.383663326501846], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 32}, {"name": "AM98_005_fire_eu_blinkers", "translation": [2.36055484492681e-07, -2.52182269096375, -0.0652636736631393], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 33}, {"name": "AM98_005_fire_eu_orange_light", "translation": [-5.5890795920277e-06, -7.63842391967773, -0.383663326501846], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 34}, {"name": "AM98_005_fire_eu_orange", "translation": [2.05025997956909e-07, -2.64196157455444, -0.915250241756439], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 35}, {"name": "AM98_005_fire_eu_black_shiny", "translation": [2.36055484492681e-07, -2.52182269096375, -0.0652636736631393], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 21}, {"name": "AM98_005_fire_eu_grill", "translation": [2.05025997956909e-07, -2.64196157455444, -0.915250241756439], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 22}, {"name": "AM98_005_fire_eu_rubber", "translation": [0.147124573588371, 1.92145943641663, 1.16986346244812], "rotation": [0.0, 0.0, 0.0, 1.0], "scale": [1.0, 1.0, 1.0], "mesh": 37}], "extensions": {"KHR_lights_punctual": {"lights": [{"name": "", "color": [1.0, 1.0, 1.0], "intensity": 1.0, "type": "point"}, {"name": "", "color": [0.999058306217194, 0.891782104969025, 0.759487330913544], "intensity": 1.0, "type": "directional"}]}}}