/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.25/esri/copyright.txt for details.
*/
import{_ as r}from"../chunks/tslib.es6.js";import{A as t}from"../chunks/Analysis.js";import{e as s}from"../core/lang.js";import{L as e}from"../chunks/Logger.js";import{a as o,i}from"../chunks/maybe.js";import{m as p}from"../chunks/unitUtils.js";import{property as n}from"../core/accessorSupport/decorators/property.js";import"../chunks/ensureType.js";import{subclass as m}from"../core/accessorSupport/decorators/subclass.js";import c from"../geometry/Polygon.js";import"../core/Accessor.js";import"../core/Handles.js";import"../chunks/get.js";import"../chunks/utils.js";import"../chunks/handleUtils.js";import"../chunks/metadata.js";import"../chunks/object.js";import"../chunks/ArrayPool.js";import"../chunks/tracking.js";import"../chunks/watch.js";import"../core/scheduling.js";import"../chunks/nextTick.js";import"../core/promiseUtils.js";import"../core/Error.js";import"../config.js";import"../chunks/string.js";import"../core/Clonable.js";import"../core/Identifiable.js";import"../core/JSONSupport.js";import"../chunks/jsonMap.js";import"../chunks/projectionEllipsoid.js";import"../geometry/SpatialReference.js";import"../chunks/writer.js";import"../chunks/Ellipsoid.js";import"../geometry/Extent.js";import"../geometry/Geometry.js";import"../chunks/reader.js";import"../geometry/Point.js";import"../core/accessorSupport/decorators/cast.js";import"../geometry/support/webMercatorUtils.js";import"../chunks/extentUtils.js";import"../chunks/zmUtils.js";let l=class extends t{constructor(r){super(r),this.type="area-measurement",this.unit=null}set geometry(r){o(r)?this._set("geometry",null):(r.rings.length>1&&e.getLogger(this.declaredClass).warn("Measuring polygons with multiple rings is not supported."),this._set("geometry",r.clone()))}get requiredPropertiesForEditing(){if(i(this.geometry)&&1===this.geometry.rings.length){const r=this.geometry.rings[0];if(r.length<=2||!s(r[0],r[r.length-1]))return[null]}return[this.geometry]}clear(){this.geometry=null}};r([n({type:["area-measurement"]})],l.prototype,"type",void 0),r([n({value:null,type:c})],l.prototype,"geometry",null),r([n({type:p,value:null})],l.prototype,"unit",void 0),r([n({readOnly:!0})],l.prototype,"requiredPropertiesForEditing",null),l=r([m("esri.analysis.AreaMeasurementAnalysis")],l);const u=l;export{u as default};
