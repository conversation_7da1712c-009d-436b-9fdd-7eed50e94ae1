/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.25/esri/copyright.txt for details.
*/
import{_ as t}from"../chunks/tslib.es6.js";import{A as s}from"../chunks/Analysis.js";import e from"./DimensionSimpleStyle.js";import o from"./LengthDimension.js";import r from"../core/Collection.js";import{r as i,c as n}from"../chunks/collectionUtils.js";import{a as p,i as m,u as c}from"../chunks/maybe.js";import{watch as l,syncAndInitial as u}from"../core/reactiveUtils.js";import{property as a}from"../core/accessorSupport/decorators/property.js";import"../core/lang.js";import"../chunks/ensureType.js";import{subclass as j}from"../core/accessorSupport/decorators/subclass.js";import h from"../geometry/Extent.js";import{projectOrLoadMany as d}from"../geometry/projection.js";import"../core/Accessor.js";import"../core/Handles.js";import"../chunks/get.js";import"../chunks/utils.js";import"../chunks/handleUtils.js";import"../chunks/metadata.js";import"../chunks/object.js";import"../chunks/Logger.js";import"../config.js";import"../chunks/string.js";import"../chunks/ArrayPool.js";import"../chunks/tracking.js";import"../chunks/watch.js";import"../core/scheduling.js";import"../chunks/nextTick.js";import"../core/promiseUtils.js";import"../core/Error.js";import"../core/Clonable.js";import"../core/Identifiable.js";import"../core/JSONSupport.js";import"../Color.js";import"../chunks/colorUtils.js";import"../chunks/mathUtils.js";import"../chunks/vec3.js";import"../chunks/common.js";import"../chunks/screenUtils.js";import"../geometry.js";import"../geometry/Geometry.js";import"../chunks/reader.js";import"../geometry/SpatialReference.js";import"../chunks/writer.js";import"../geometry/Multipoint.js";import"../geometry/Point.js";import"../core/accessorSupport/decorators/cast.js";import"../geometry/support/webMercatorUtils.js";import"../chunks/Ellipsoid.js";import"../chunks/zmUtils.js";import"../geometry/Polygon.js";import"../chunks/extentUtils.js";import"../geometry/Polyline.js";import"../chunks/typeUtils.js";import"../chunks/jsonMap.js";import"../geometry/support/jsonUtils.js";import"../chunks/Cyclical.js";import"../core/Evented.js";import"../chunks/shared.js";import"../chunks/SimpleObservable.js";import"../chunks/unitUtils.js";import"../chunks/projectionEllipsoid.js";import"../chunks/mat4.js";import"../chunks/pe.js";import"../chunks/assets.js";import"../request.js";import"../kernel.js";import"../core/urlUtils.js";import"../chunks/aaBoundingRect.js";import"../chunks/geodesicConstants.js";import"../geometry/support/GeographicTransformation.js";import"../geometry/support/GeographicTransformationStep.js";import"../chunks/zscale.js";const y=r.ofType(o);let k=class extends s{constructor(t){super(t),this.type="dimension",this.style=new e,this.extent=null}initialize(){this.addHandles(l((()=>this._computeExtent()),(t=>{(p(t)||p(t.pending))&&this._set("extent",m(t)?t.extent:null)}),u))}get dimensions(){return this._get("dimensions")||new y}set dimensions(t){this._set("dimensions",i(t,this.dimensions,y))}get spatialReference(){for(const t of this.dimensions){if(m(t.startPoint))return t.startPoint.spatialReference;if(m(t.endPoint))return t.endPoint.spatialReference}return null}get requiredPropertiesForEditing(){return this.dimensions.reduce(((t,s)=>(t.push(s.startPoint,s.endPoint),t)),[])}async waitComputeExtent(){const t=this._computeExtent();return m(t)?c(t.pending):null}_computeExtent(){const t=this.spatialReference;if(p(t))return{pending:null,extent:null};const s=[];for(const t of this.dimensions)m(t.startPoint)&&s.push(t.startPoint),m(t.endPoint)&&s.push(t.endPoint);const e=d(s,t);if(m(e.pending))return{pending:e.pending,extent:null};let o=null;return m(e.geometries)&&(o=e.geometries.reduce(((t,s)=>p(t)?m(s)?h.fromPoint(s):null:m(s)?t.union(h.fromPoint(s)):t),null)),{pending:null,extent:o}}clear(){this.dimensions.removeAll()}};t([a({type:["dimension"]})],k.prototype,"type",void 0),t([a({cast:n,type:y,nonNullable:!0})],k.prototype,"dimensions",null),t([a({readOnly:!0})],k.prototype,"spatialReference",null),t([a({types:{key:"type",base:null,typeMap:{simple:e}},nonNullable:!0})],k.prototype,"style",void 0),t([a({value:null,readOnly:!0})],k.prototype,"extent",void 0),t([a({readOnly:!0})],k.prototype,"requiredPropertiesForEditing",null),k=t([j("esri.analysis.DimensionAnalysis")],k);const g=k;export{g as default};
