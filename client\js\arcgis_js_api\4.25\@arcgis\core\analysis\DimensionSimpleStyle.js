/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.25/esri/copyright.txt for details.
*/
import{_ as o}from"../chunks/tslib.es6.js";import s from"../Color.js";import{Clonable as r}from"../core/Clonable.js";import{JSONSupportMixin as t}from"../core/JSONSupport.js";import{t as e,p as i}from"../chunks/screenUtils.js";import{property as p}from"../core/accessorSupport/decorators/property.js";import"../core/lang.js";import{I as n}from"../chunks/ensureType.js";import{subclass as c}from"../core/accessorSupport/decorators/subclass.js";import"../chunks/colorUtils.js";import"../chunks/mathUtils.js";import"../chunks/vec3.js";import"../chunks/common.js";import"../chunks/maybe.js";import"../chunks/object.js";import"../chunks/Logger.js";import"../config.js";import"../chunks/string.js";import"../core/Accessor.js";import"../core/Handles.js";import"../chunks/get.js";import"../chunks/utils.js";import"../chunks/handleUtils.js";import"../chunks/metadata.js";import"../chunks/ArrayPool.js";import"../chunks/tracking.js";import"../chunks/watch.js";import"../core/scheduling.js";import"../chunks/nextTick.js";import"../core/promiseUtils.js";import"../core/Error.js";let m=class extends(t(r)){constructor(o){super(o),this.type="simple",this.color=new s("black"),this.lineSize=2,this.fontSize=10,this.textColor=new s("black"),this.textBackgroundColor=new s([255,255,255,.6])}};o([p({type:["simple"],readOnly:!0,json:{write:{isRequired:!0}}})],m.prototype,"type",void 0),o([p({type:s,nonNullable:!0,json:{type:[n],write:{isRequired:!0}}})],m.prototype,"color",void 0),o([p({type:Number,cast:e,nonNullable:!0,range:{min:i(1)},json:{write:{isRequired:!0}}})],m.prototype,"lineSize",void 0),o([p({type:Number,cast:e,nonNullable:!0,json:{write:{isRequired:!0}}})],m.prototype,"fontSize",void 0),o([p({type:s,nonNullable:!0,json:{type:[n],write:{isRequired:!0}}})],m.prototype,"textColor",void 0),o([p({type:s,nonNullable:!0,json:{type:[n],write:{isRequired:!0}}})],m.prototype,"textBackgroundColor",void 0),m=o([c("esri.analysis.DimensionSimpleStyle")],m);const l=m;export{l as default};
