/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.25/esri/copyright.txt for details.
*/
import{_ as t}from"../chunks/tslib.es6.js";import"../geometry.js";import{ClonableMixin as o}from"../core/Clonable.js";import{c as s}from"../chunks/Cyclical.js";import{JSONSupport as r}from"../core/JSONSupport.js";import{h as i}from"../chunks/maybe.js";import{property as e}from"../core/accessorSupport/decorators/property.js";import"../core/lang.js";import{cast as p}from"../core/accessorSupport/decorators/cast.js";import{subclass as m}from"../core/accessorSupport/decorators/subclass.js";import{l as n}from"../chunks/ensureType.js";import{p as c}from"../chunks/persistable.js";import l from"../geometry/Point.js";import"../geometry/Extent.js";import"../chunks/string.js";import"../chunks/object.js";import"../geometry/Geometry.js";import"../chunks/reader.js";import"../geometry/SpatialReference.js";import"../chunks/writer.js";import"../core/Accessor.js";import"../core/Handles.js";import"../chunks/get.js";import"../chunks/utils.js";import"../chunks/handleUtils.js";import"../chunks/metadata.js";import"../chunks/Logger.js";import"../config.js";import"../chunks/ArrayPool.js";import"../chunks/tracking.js";import"../chunks/watch.js";import"../core/scheduling.js";import"../chunks/nextTick.js";import"../core/promiseUtils.js";import"../core/Error.js";import"../geometry/support/webMercatorUtils.js";import"../chunks/Ellipsoid.js";import"../geometry/Multipoint.js";import"../chunks/zmUtils.js";import"../geometry/Polygon.js";import"../chunks/extentUtils.js";import"../geometry/Polyline.js";import"../chunks/typeUtils.js";import"../chunks/jsonMap.js";import"../geometry/support/jsonUtils.js";import"../chunks/mathUtils.js";import"../chunks/vec3.js";import"../chunks/common.js";import"../chunks/multiOriginJSONSupportUtils.js";import"../core/urlUtils.js";import"../chunks/uuid.js";import"../chunks/persistableUrlUtils.js";let h=class extends(o(r)){constructor(t){super(t),this.type="plane",this.position=null,this.heading=0,this.tilt=0,this.width=10,this.height=10}equals(t){return this.heading===t.heading&&this.tilt===t.tilt&&i(this.position,t.position)&&this.width===t.width&&this.height===t.height}};t([e({readOnly:!0,json:{read:!1,write:!0}})],h.prototype,"type",void 0),t([e({type:l}),c()],h.prototype,"position",void 0),t([e({type:Number,nonNullable:!0,range:{min:0,max:360}}),c(),p((t=>s.normalize(n(t),0,!0)))],h.prototype,"heading",void 0),t([e({type:Number,nonNullable:!0,range:{min:0,max:360}}),c(),p((t=>s.normalize(n(t),0,!0)))],h.prototype,"tilt",void 0),t([e({type:Number,nonNullable:!0}),c()],h.prototype,"width",void 0),t([e({type:Number,nonNullable:!0}),c()],h.prototype,"height",void 0),h=t([m("esri.analysis.SlicePlane")],h);const u=h;export{u as default};
