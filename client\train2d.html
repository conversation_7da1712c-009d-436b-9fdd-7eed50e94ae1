<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="initial-scale=1, maximum-scale=1, user-scalable=no" />
    <title>消防立体化协同标绘系统_2D模式</title>
    <!-- <link rel="stylesheet" href="https://js.arcgis.com/4.24/esri/themes/light/main.css" /> -->
    <link rel="stylesheet" href="./js/arcgis_js_api/4.25/esri/themes/light/main.css" />
    <link rel="stylesheet" href="./layui/css/layui.css" />
    <link rel="stylesheet" href="./css/style.css" />
    <link rel="icon" type="image/x-icon" href="./images/favicon.ico">
    <!-- 加载模块专用css -->
    <link rel="stylesheet" href="./modules/css/exerciseInfo.css" />
    <link rel="stylesheet" href="./modules/css/publishInfo.css" />
    <link rel="stylesheet" href="./modules/css/modelClassifyManage.css" />
    <link rel="stylesheet" href="./css/jquery-ui.css">


    <script src="./data/config.js"></script>
    <script src="./js/jquery-1.11.0.min.js"></script>
    <script src="./js/jquery-ui.min.js"></script>
    <script src="./js/html2canvas.min.js"></script>
    <script src="./layui/layui.js"></script>
    <!--WebSocket通讯-->
    <script src="./lib/sockjs.min.js"></script>
    <script src="./lib/stomp.min.js"></script>
    <script src="./js/socketInfo.js"></script>
    <!-- <script src="https://js.arcgis.com/4.24/"></script> -->
    <script src="./js/arcgis_js_api/4.25/init.js"></script>
    <script src="./js/arcgisdraw2d_train.js"></script>
    <script src="./js/index2d_train.js"></script>

    <!-- 最后加载模块专用JS -->
    <script src="./modules/js/exerciseInfo.js"></script>
    <script src="./modules/js/publishInfo.js"></script>
    <script src="./modules/js/modelClassifyManage.js"></script>
</head>

<body>
    <div class="header" style="display:flex; justify-content: space-between;">
        <div style="color:white ;z-index: 10;">
            <span style="vertical-align:middle;padding-left: 5px;"><img src="images/logo.png" width="50"
                    style="padding: 5px;"></span>
            <span class="title" style="vertical-align:middle;" id="projectTitle">>消防立体化协同标绘系统_2D模式</span>
            <span id="div_modelMenu" class="menu" onclick="hideModelDiv()"><img src="images/model2.png" width="24"
                    style="margin-top: -5px;"> 模型列表 |</span>
            <span id="div_layerMenu" class="menu" onclick="hideLayerDiv()"><img src="images/layers.png" width="24"
                    style="margin-top: -5px;"> 图层管理 |</span>
            <span id="div_sceneImgMenu" class="menu" onclick="hideSceneImgDiv()"><img src="images/cutImg.png" width="24"
                    style="margin-top: -5px;"> 截图管理</span>
            <!-- <span class="menu" onclick="hideSceneDiv()"><img src="images/scene.png" width="24"
                    style="margin-top: -5px;"> 副本管理 </span> -->
        </div>

        <!-- <div style="vertical-align: middle; padding-left: 100px;font-size: 18px;color: #ffffff;font-weight: bold;">
            <input type="button" value="切换3D模式" onclick="open3D()" class="btn"
                style="font-size: 14px; width: 100px; height: 40px;margin: 10px 10px 10px 0px;"></input>
        </div> -->
        <div style="color: white; z-index: 10; display: flex">
            <img src="images/header_app.jpg" width="80" class="userImg" id="userAvatar" />
            <div style="padding-top: 20px;padding-right: 20px;">
                <span class="userInfo" id="userSpan"></span>
            </div>
        </div>
    </div>
    <div id="viewDiv" style="position:fixed;top:60px;left:330px;bottom:0px;right: 290px;"></div>

    <div id="div_tools" class="tool" style="height:auto;">
        <div style="margin-left: 5px;display:flex;">
            <!-- <img src="images/jingbao.png" width="32" height="32" style="margin-top: -3px;"> -->
            <label id="labJQBH" style="width: 98%; font-size: 14px; margin: auto;"></label>
            <div onclick="centerToEvent()" class="img-locate" style="margin: auto;"></div>
            <div onclick="closeSceneCopy()" class="img-closeCopy" id="btn_closeCopy"></div>
        </div>
        <div style="margin-left: 10px;display:flex;">
            <div onclick="fullView()" class="img-fullView" id="fullViewBtn"></div>
        </div>
        <div style="display: none;">
            <ol style="display: flex; list-style: none;margin: 0px; padding: 0px;">
                <li style="padding: 0px 5px 0px 5px;">
                    <span style="vertical-align:middle;" class="custom-checkbox">
                        <input id="ck_cedit" type="checkbox" value="" checked onchange="setLabelVis(this.checked)"
                            style="height: 18px;width: 18px;margin-top: 5px;"></span>
                    <span style="vertical-align:middle;" title="方便快速的显示隐藏当前地图的注记信息，需要手动点击切换。">显隐注记</span>
                </li>
            </ol>
        </div>
    </div>

    <div id="layerDiv"
        style="position:fixed;width:330px;top:60px;bottom:0px;left:0px;background-color: white; border: 2px solid #084474;z-index: 2;display: none;">
        <div class="content-title">
            <span>图层列表</span>
            <span style="position: fixed;margin-left: 105px;margin-top:-7px;"><img src="images/close.png" width="30"
                    onclick="hideLayerDiv()" style="cursor:pointer;"></span>
        </div>
        <div style="position:absolute;bottom:0px; top: 35px;overflow-y: auto;">
            <fieldset class="layui-elem-field" style="width: 300px;padding: 5px 10px 10px 10px;">
                <legend style="font-size: 16px;padding: 10px 0px 5px 0px;">
                    <img src="images/leaf.png" width="32px">
                    图片图层信息
                </legend>

                <div>
                    <input id="upImg" type="button" value="加载图片" class="btn" style="width: 140px;"></input>
                    <input id="locationImg" type="button" value="定位范围" class="btn" style="width: 140px;"
                        onclick="locationExtent()"></input>
                    <div class="layui-upload-list">
                        <img class="layui-upload-img" id="demo1" height="100" width="240" style="display: none;">
                        <p id="demoText"></p>
                    </div>
                    <div style="width: 300px;">
                        <div class="layui-progress layui-progress-big" lay-showpercent="yes" lay-filter="demo">
                            <div class="layui-progress-bar" lay-percent=""></div>
                        </div>
                    </div>
                    <div>
                        <p style="padding: 10px 0px 10px 0px;"><span style="vertical-align:middle">图片显隐：</span>
                            <span style="vertical-align:middle" class="custom-checkbox"><input id="img_visible"
                                    type="checkbox" value="" onchange="setExtent()" style="width: 20px;height: 20px;"
                                    checked></span>
                        </p>
                        <p> 最小经度：<input id="spatial_xmin" style="width: 180px;" class="txt" step="0.01" type="number"
                                value="" onkeyup="setExtent()" onchange="setExtent()"></p>
                        <p> 最小纬度：<input id="spatial_ymin" style="width: 180px;" class="txt" step="0.01" type="number"
                                value="" onkeyup="setExtent()" onchange="setExtent()"></p>
                        <p> 最大经度：<input id="spatial_xmax" style="width: 180px;" class="txt" step="0.01" type="number"
                                value="" onkeyup="setExtent()" onchange="setExtent()"></p>
                        <p> 最大纬度：<input id="spatial_ymax" style="width: 180px;" class="txt" step="0.01" type="number"
                                value="" onkeyup="setExtent()" onchange="setExtent()"></p>
                    </div>
                </div>
            </fieldset>
            <fieldset class="layui-elem-field" style="padding: 5px 10px 10px 10px;">
                <legend style="font-size: 16px;padding: 10px 0px 5px 0px;">
                    <img src="images/location.png" width="32px">
                    地图图层信息
                </legend>
                <div id="treeLayer"></div>
            </fieldset>
        </div>
    </div>
    <div id="sceneDiv"
        style="position:fixed;width:330px;top:60px;bottom:0px;left:0px;background-color: white; border: 2px solid #084474;z-index: 2;display: none;">
        <div class="content-title">
            <span>副本列表</span>
            <span style="position: fixed;margin-left: 105px;margin-top:-7px;"><img src="images/close.png" width="30"
                    onclick="hideSceneDiv()" style="cursor:pointer;"></span>
        </div>
        <div style="text-align:center;padding-top: 5px;">
            <input type="button" value="保存副本" onclick="saveSceneClick()" class="btn" style="width: 300px;"></input>
        </div>
        <div style="position:absolute;bottom:0px; top: 70px;overflow-y: auto;">
            <ol class='ol-scene-grid' id='left_scenes' style="color:black;font-size: 12px;">
            </ol>
        </div>
    </div>

    <div id="sceneImgDiv"
        style="position:fixed;width:290px;top:60px;bottom:0px;right:0px;background-color: white; border: 2px solid #084474;z-index: 2;display: none;">
        <div id="load-sceneImgDiv" style="position:fixed;width:290px;top:60px;bottom:0px;right:0px;background-color: rgb(83, 83, 83,0.5); border: 2px solid #084474;z-index: 2;align-content: center;
        justify-items: center;display: none;">
            <img src="images/loading.gif" width="64">
            <label id="load-sceneImgLab" style="font-size: 18px;color:white">分享截图中，请稍候......</label>
        </div>
        <div class="content-title">
            <span>场景截图</span>
            <span style="position: fixed;margin-left: 75px;margin-top: -7px;"><img src="images/close.png" width="30"
                    onclick="hideSceneImgDiv()" style="cursor:pointer;"></span>
        </div>
        <div style="text-align:center;padding-top: 5px;">
            <input type="button" value="截图" onclick="saveImgClick()" class="btn" style="width: 200px;"></input>
        </div>
        <div style="position:absolute;bottom:0px; top: 70px;overflow-y: auto;">
            <ol class='app-grid' id='right_imgs' style="color:black;font-size: 12px;">
                <!-- <li class="li-img-content" >
                    <img class="li-img-close" src="images/close.png" >
                    <span style="vertical-align:middle;padding-right: 10px;" >
                        <img src="images/jt.png" height="48" width="80">
                    </span>
                    <span style="vertical-align:middle;font-size: 14px;" >
                        西-东-2022-8-29 9:10:46 中心点：118.158993 24.481558
                    </span>
                </li> -->
            </ol>
            <div id="photoPage" style="margin-left: 5px;"></div>
        </div>
    </div>
    <div id="div_modelList"
        style="position:fixed;width:330px;top:60px;bottom:0px;left:0px;background-color: white; border: 2px solid #084474">
        <div class="content-title">
            <span>模型列表</span>
            <span style="position: fixed;margin-left: 105px;margin-top:-2px;"><img src="images/settings.png" width="22"
                    height="22" onclick="handleModalIconManage()" style="cursor:pointer;"></span>
        </div>
        <div style="position:absolute;bottom:0px; top: 31px ;width: 40px; border-right: 2px solid #084474;height: calc(100vh - 90px);overflow-y: auto;">
            <ol class='menu-grid' id='left_frame'>
                <!-- <li id='cytbmx' class='menu-li-content' onclick='scrollwin(this)'>
                    <img src="images/消防标号/redu.png" class="menu-li-image" />
                    常用图标
                </li>
                <li id='shape' class='menu-li-content' onclick='scrollwin(this)'>
                    <img src="images/消防标号/shoucang.png" class="menu-li-image" />
                    军标图形
                </li>
                <li id='jbry' class='menu-li-content' onclick='scrollwin(this)'>
                    <img src="images/消防标号/renwu.png" class="menu-li-image" />
                    级别人员
                </li>
                <li id='xfclqczb' class='menu-li-content' onclick='scrollwin(this)'>
                    <img src="images/消防标号/huiyuan_1.png" class="menu-li-image" />
                    消防车辆器材装备
                </li>
                <li id='zdxd' class='menu-li-content' onclick='scrollwin(this)'>
                    <img src="images/消防标号/shandian.png" class="menu-li-image" />
                    战斗行动
                </li>
                <li id='xfsy' class='menu-li-content' onclick='scrollwin(this)'>
                    <img src="images/消防标号/dianzan.png" class="menu-li-image" />
                    消防水源
                </li>
                <li id='xfss' class='menu-li-content' onclick='scrollwin(this)'>
                    <img src="images/消防标号/xiangji_1.png" class="menu-li-image" />
                    灭火器与灭火设施
                </li>
                <li id='cyjz' class='menu-li-content' onclick='scrollwin(this)'>
                    <img src="images/消防标号/anquan_1.png" class="menu-li-image" />
                    常用建筑及构件
                </li> -->

            </ol>
        </div>
        <div style="position:absolute; top: 36px;left:47px ;height: 20px;">
            <input id="txtSearch" type="text" class="txt" value="" style="height: 15px;width: 190px;"
                onkeyup="queryModels()">
            <input type="button" value="搜索" onclick="queryModels()" class="btn" style="width: 50px;"></input>
        </div>
        <div id="left_models" style="position:absolute;bottom:0px; top: 68px;left:40px ;overflow-y: auto;">
            <!-- <ol class='app-grid' id='left_app'>
                <li>
                    <div class="map-select" style="background-image: url(images/leaf.png);" onclick="sketchGraphic('img')">
                        <div class="map-select-info">
                            图标标注
                        </div>
                    </div>
                </li> 
            </ol> -->
        </div>
    </div>
    <div id="attributesDiv"
        style="position:fixed;width:290px;top:60px;bottom:0px;right:0px;background-color: white;border: 2px solid #084474;">
        <div class="layui-tab layui-tab-brief" lay-filter="attrTab">
            <ul class="layui-tab-title">
                <li class="layui-this" id="infoTab">属性信息</li>
                <li id="timeTab" lay-id="timeTab">演练时间轴</li>
                <li id="userTab">在线人员</li>
            </ul>
            <div class="layui-tab-content" style="padding: 0px; width: 270px;">
                <div class="layui-tab-item layui-show"
                    style="overflow-x: hidden;overflow-y:auto;position:absolute;bottom:0px; top: 48px;">
                    <fieldset class="layui-elem-field">
                        <legend style="font-size: 16px;padding: 10px 0px 5px 0px;">
                            <img src="images/编辑.png" width="24px">
                            基本信息
                        </legend>
                        <table>
                            <tr style="background-color:#084474;">
                                <td class="td-title" style="color:#ffffff;font-weight: bold;">用户名称：</td>
                                <td style="padding-right: 5px;"><input id="user_name" class="txt" type="text" value=""
                                        readonly>
                                </td>
                            </tr>
                            <tr style="background-color:#084474;">
                                <td class="td-title" style="color:#ffffff;font-weight: bold;">标绘时间：</td>
                                <td style="padding-right: 5px;"><input id="user_time" class="txt" type="text" value=""
                                        readonly>
                                </td>
                            </tr>
                            <tr>
                                <td class="td-title">物体标识：</td>
                                <td><input id="wt_id" class="txt" type="text" value="" readonly></td>
                            </tr>
                            <tr>
                                <td class="td-title">物体名称：</td>
                                <td>
                                    <textarea id="wt_name" class="txt" maxlength="200"
                                        style="height: 50px;resize: none;" onkeyup="txtKeyup()" onchange="txtKeyup()"
                                        readonly></textarea>
                                    <!-- <input id="wt_name" class="txt" type="text" value="" onkeyup="txtKeyup()"
                                onchange="txtKeyup()"> -->
                                </td>
                            </tr>
                            <tr style="display: none;">
                                <td class="td-title">物体长度：</td>
                                <td><input id="wt_length" class="txt" type="number" title="" value=""
                                        onkeyup="txtKeyup()" onchange="txtKeyup()" readonly></td>
                            </tr>
                            <tr id="tr_wt_hw">
                                <td class="td-title">物体比例：</td>
                                <td style="display: flex;">
                                    <div id="wt_hw" style="margin: 20px 0px 20px 0px;width: 180px;"></div>
                                    <span style="line-height: 45px; font-size: 18px;">%</span>
                                </td>
                            </tr>
                            <tr id="tr_wt_angle">
                                <td class="td-title">物体角度：</td>
                                <td>
                                    <div id="wt_angle" style="margin: 20px 0px 20px 0px;width: 180px;"></div>
                                </td>
                            </tr>
                        </table>
                        <div style="text-align:center;padding-top: 5px;">
                            <button id="btn_delGrap" type="button"
                                class="layui-btn layui-btn-sm layui-bg-blue layui-btn-disabled" style="width: 250px;"
                                onclick="deleteGraphic()">删除对象</button>
                        </div>
                    </fieldset>
                    <fieldset class="layui-elem-field">
                        <legend style="font-size: 16px;padding: 0px 0px 5px 0px;">
                            <img src="images/位置.png" width="24px">
                            空间要素相关
                        </legend>
                        <table>
                            <tr id="tr_spatial_length">
                                <td class="td-title">周长：</td>
                                <td><input id="spatial_length" class="txt" type="number" step="0.01"
                                        style="width: 130px;" readonly> m</td>
                            </tr>
                            <tr id="tr_spatial_area">
                                <td class="td-title">面积：</td>
                                <td><input id="spatial_area" class="txt" type="number" step="0.01" style="width: 130px;"
                                        readonly>
                                    ㎡</td>
                            </tr>
                            <tr id="tr_spatial_lon">
                                <td class="td-title">经度：</td>
                                <td><input id="spatial_lon" class="txt" type="number" step="0.01" value=""
                                        onkeyup="txtKeyup('jwd')" onchange="txtKeyup('jwd')" title="" readonly></td>
                            </tr>
                            <tr id="tr_spatial_lat">
                                <td class="td-title">纬度：</td>
                                <td><input id="spatial_lat" class="txt" step="0.01" type="number" value=""
                                        onkeyup="txtKeyup('jwd')" onchange="txtKeyup('jwd')" title="" readonly>
                                </td>
                            </tr>

                            <tr style="display: none;">
                                <td class="td-title">符号地址：</td>
                                <td><input id="img_url" class="txt" type="text" value="" onkeyup="txtKeyup()"
                                        onchange="txtKeyup()" readonly></td>
                            </tr>
                            <tr id="tr_border_width">
                                <td class="td-title">边线宽度：</td>
                                <td>
                                    <div id="border_width" style="margin: 15px 0px 15px 0px;width: 200px;"></div>
                                </td>
                            </tr>
                            <tr id="tr_border_color">
                                <td class="td-title" style="height: 35px;">边线颜色：</td>
                                <td>
                                    <div id="border_color"></div>
                                </td>
                            </tr>
                            <tr id="tr_fill_color">
                                <td class="td-title" style="height: 35px;">填充颜色：</td>
                                <td>
                                    <div id="fill_color"></div>
                                </td>
                            </tr>
                            <tr id="tr_fill_url">
                                <td class="td-title" style="height: 75px;">填充图案：</td>
                                <td>
                                    <ul id="ul_fill_url"
                                        style="list-style: none; display: flex;width: 190px; overflow-x: auto;background-color: rgba(192, 192, 187, 0.589);border-radius: 8px;">
                                        <!-- <li class="li-fill-Img" fillUrl="images/copper.png" onclick="filimgClick(this)">
                                    <img src="images/copper.png" width="64" height="64">
                                </li>
                                <li class="li-fill-Img" fillUrl="images/flower2.png" onclick="filimgClick(this)">
                                    <img src="images/flower2.png" width="64" height="64">
                                </li>
                                <li class="li-fill-Img" fillUrl="images/leaf.png" onclick="filimgClick(this)">
                                    <img src="images/leaf.png" width="64" height="64">
                                </li>
                                <li class="li-fill-Img" fillUrl="images/letter.png" onclick="filimgClick(this)">
                                    <img src="images/letter.png" width="64" height="64">
                                </li> -->
                                    </ul>

                                    <input id="fill_url" class="txt" type="text" value="" onkeyup="txtKeyup()"
                                        onchange="txtKeyup()" style="display:none;">
                                </td>
                            </tr>
                            <tr id="tr_fill_url_width">
                                <td class="td-title">图案宽高：</td>
                                <td><input id="fill_url_width" style="width: 55px;" class="txt" type="number" step="1"
                                        value="64" onkeyup="txtKeyup()" onchange="txtKeyup()" readonly> *
                                    <input id="fill_url_height" style="width: 55px;" class="txt" type="number" step="1"
                                        value="64" onkeyup="txtKeyup()" onchange="txtKeyup()" readonly>
                                </td>
                            </tr>
                        </table>
                    </fieldset>
                    <fieldset class="layui-elem-field">
                        <legend style="font-size: 16px;padding: 0px 0px 5px 0px;">
                            <img src="images/标签.png" width="24px">
                            注记相关
                        </legend>
                        <table>
                            <tr>
                                <td class="td-title layui-input-block">注记显隐：</td>
                                <td class="custom-checkbox"><input id="label_show" type="checkbox" value=""
                                        onkeyup="txtKeyup()" onchange="txtKeyup()" style="width: 20px;height: 20px;">
                                </td>
                            </tr>
                             <tr id="tr_label_bcolor">
                                <td class="td-title" style="height: 35px;">注记颜色：</td>
                                <td>
                                    <div id="label_bcolor"></div>
                                </td>
                            </tr>
                            <tr>
                                <td class="td-title" style="padding: 15px 0px 10px 0px;">对齐方式：</td>
                                <td style="padding: 10px 0px 10px 0px;" class="custom-radio">
                                    <input type="radio" name="label_align" value="left" title="左" checked="true"
                                        style="width: 18px;height: 18px;;vertical-align: middle;" onclick="txtKeyup()">
                                    左
                                    <input type="radio" name="label_align" value="center" title="中"
                                        style="width: 18px;height: 18px; ;vertical-align: middle;" onclick="txtKeyup()">
                                    中
                                    <input type="radio" name="label_align" value="right" title="右"
                                        style="width: 18px;height: 18px; ;vertical-align: middle;" onclick="txtKeyup()">
                                    右
                                </td>
                            </tr>
                            <tr>
                                <td class="td-title">注记高度： </td>
                                <td><input id="label_height" class="txt" type="number" title="" step="1"
                                        onkeyup="txtKeyup()" onchange="txtKeyup()" style="width: 50px;"></td>
                            </tr>
                            <tr>
                                <td class="td-title">注记大小：</td>
                                <td>
                                    <div id="label_size" style="margin: 20px 0px 20px 0px;width: 200px;"></div>
                                </td>
                            </tr>
                            <!-- <tr>
                        <td class="td-title">注记背景：</td>
                        <td>
                            <div id="label_bcolor"></div>
                        </td>
                    </tr> -->
                        </table>
                    </fieldset>
                </div>
                <div class="layui-tab-item"
                    style="overflow-x: hidden;overflow-y:auto;position:absolute;bottom:0px; top: 50px; width: 290px;">
                    <div class="timeline-container" style="padding: 10px 2px;">
                        <ul class="layui-timeline">
                            <li class="layui-timeline-item no-data-item">
                                <div class="layui-text" style="text-align: center;">
                                    <p style="margin: 5px 0;color:#8e939b">暂无数据</p>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="layui-tab-item"
                    style="overflow-x: hidden;overflow-y:auto;position:absolute;bottom:0px; top: 50px;width: 100%;">
                    <div class="online-users-container" style="margin: 0px 10px; width: 98%; ">
                        <ul class="layui-nav layui-nav-tree"
                            style="margin-top: 10px; width: 95%;background-color: #ffffff;" id="onlineUsersList">
                            <!-- 在线用户将通过JavaScript动态添加 -->
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="userline" style="display: none;">
        <div class="legend" onclick="showLgend('click')">
            <label>用户图例</label>
            <img id="img_legend" src="images/向下.png" width="16" height="16">
        </div>
        <div id="tab_legend_div" style="background-color: #fff; overflow: auto;">
            <table id="tab_legend">
            </table>
        </div>
    </div>
    <div id="div_statebar" class="statebar">
        <img id="imgState" src="images/jinggao.png" width="24" height="24" style="margin: 0px 5px 0px 5px;">
        <div>
            <label id="labState">正在连接服务器……</label>
            <!-- <input type="button" value="连接" onclick="ws.connect()" class="btn"
                style="font-size: 14px; width: 60px; height: 30px;margin: 10px 10px 10px 0px;">
            </input> <input type="button" value="断开连接" onclick="ws.disconnect()" class="btn"
                style="font-size: 14px; width: 80px; height: 30px;margin: 10px 10px 10px 0px;"></input> -->
        </div>
    </div>

    <div id="bottomNav" class="bottomNav">
        <button type="button" class="layui-btn layui-bg-green" onclick="showExercInfo()">演练信息</button>
        <button type="button" class="layui-btn layui-bg-blue" onclick="showPublishInfo()">发布信息</button>
    </div>
    <!-- 弹窗 -->
    <div id="exercise-layer-wrapper" style="display: none"></div>
    <div id="publish-layer-wrapper" style="display: none"></div>
    <div id="publish-template-layer-wrapper" style="display: none"></div>
    <div id="modal-icon-classify-layer-wrapper" style="display: none"></div>
    <div id="add-category-layer-wrapper" style="display: none"></div>
    <div id="publish-info-banner-wrapper"></div>


    <div style="padding: 25px; font-size: 13px; color: #333; font-weight: 400;display: none;" id="copyDiv">
        <div>
            <label>副本名称：</label>
            <input id="txtCopyTitle" type="text" class="txt" maxlength="20" style="width: 265px; line-height: 28px" />
        </div>
        <div style="padding-top: 18px; text-align: center">
            <label class="copyName" onclick="setText(this)">开始阶段</label>
            <label class="copyName" onclick="setText(this)">进攻阶段</label>
            <label class="copyName" onclick="setText(this)">总攻阶段</label>
            <label class="copyName" onclick="setText(this)">收尾阶段</label>
        </div>
    </div>
</body>
<script>
    var opType = "pc";
    var ws = null;//WebSocket连接对象
    var txtKeyup;
    var deleteGraphic; // 删除绘制对象
    var isGoJQ = false;//是否进行了警情定位，防止断线重连后，反复定位
    var isLoadCopy = false; //是否载入过副本
    var isLoadImg = false; //是否载入过截图
    var socketConnect;//连接服务器
    var socketSend;//发送信息
    var centerToEvent;//定位到警情位置 
    var setEvetPoint;//设置警情位置，并定位
    var setAttData;//设置属性
    var displayAttData;//

    var user_name = document.getElementById("user_name");//用户姓名
    var user_time = document.getElementById("user_time");//标绘时间

    var wt_id = document.getElementById("wt_id");//物体ID
    var wt_name = document.getElementById("wt_name");//物体名称
    var wt_hw = document.getElementById("wt_hw");//物体宽高比 
    var wt_length = document.getElementById("wt_length");//物体长度
    var spatial_length = document.getElementById("spatial_length");//周长
    var spatial_area = document.getElementById("spatial_area");//面积

    var spatial_lon = document.getElementById("spatial_lon");//经度
    var spatial_lat = document.getElementById("spatial_lat");//纬度
    var spatial_height = document.getElementById("spatial_height");//高程

    var img_url = document.getElementById("img_url");//符号地址
    var label_show = document.getElementById("label_show");//注记显/隐
    var label_height = document.getElementById("label_height");//注记高度
    var fill_url = document.getElementById("fill_url");//填充图案
    var fill_url_width = document.getElementById("fill_url_width");//图案宽度
    var fill_url_height = document.getElementById("fill_url_height");//图案高度
    var colorpicker;
    var slider;
    var tree;
    var upload;
    var element;
    var fillImgJsons;



    var url = new URL(window.location.href);

    //测试token
    //var token = "";
    var token = url.searchParams.get("token");
    var jqId = url.searchParams.get("id");//警情ID
    var code = url.searchParams.get("code");//警情编号
    var lon = url.searchParams.get("lon");
    var lat = url.searchParams.get("lat");
    var content = url.searchParams.get("content");
    var r = url.searchParams.get("r");
    var role = url.searchParams.get("role");
    // console.log("警情信息：" + id, lon, lat, content, r);
    // 存储已经显示过上线消息的用户
    var shownOnlineNotifications = {};
    var onlineUserHash = {};
    var id = null;
    if (code != null) {
        id = code + "（2D）";
    }
    if (lon != null) {
        lon = parseFloat(lon);
    }
    if (lat != null) {
        lat = parseFloat(lat);
    }
    if (r != null) {
        r = parseInt(r);
    } else {
        r = 0;
    }

    // 设置模块隐藏
    if(role === '观摩人员'){
       r = 0; 
       $("#bottomNav").hide();
       $("#div_modelMenu").hide();
       $("#div_layerMenu").hide();
       $("#div_sceneImgMenu").hide();
       $("#fullViewBtn").hide();
       $("#infoTab").hide();
       $("#userTab").hide(); 
       // 切换到演练时间轴选项卡  
       layui.use('element', function(){
          var element = layui.element;  
          element.tabChange('attrTab', 'timeTab'); 
       });  

    }

    var jqJson = {
        "id": id,
        "lon": lon,
        "lat": lat,
        "content": content,
        "r": r,
        "role": role
    };

    curSceneID = jqJson.id;
    $("#labJQBH").text(jqJson.content);
    userJson.r = r;
    if (userJson.r == 0) {
        $("div_modelMenu").css("display", "none");
        setModelList(false);
    }

    function setText(ele) {
        document.getElementById("txtCopyTitle").value = ele.innerText;
    }

    function getText() {
        return document.getElementById("txtCopyTitle").value;
    }


    layui.use(['colorpicker', 'slider', 'upload', 'element', 'dropdown'], function () {
        colorpicker = layui.colorpicker;
        slider = layui.slider;
        tree = layui.tree;
        upload = layui.upload;
        element = layui.element;

        // 初始化Tab
        element.on('tab(attrTab)', function (data) {
            console.log('Tab切换到：' + data.index);
            if (data.index === 2) { // 在线人员 tab
                // 刷新在线人员列表
                $("#tab_legend tr").each(function () {
                    if ($(this).attr('id')) {
                        var userId = $(this).attr('id').replace('tr_', '');
                        var colorDiv = $(this).find('div.legend-color');
                        var color = colorDiv.css('background-color');
                        var userNameCell = $(this).find('td:eq(1)');
                        var userName = userNameCell.text();
                        var status = userNameCell.css('color') === 'rgb(207, 207, 207)' ? "0" : "1"; // 灰色为离线

                        setTabLegend(userId, userName, status, color);
                    }
                });
            }
        });

        // 系统标题
        var pName = projectName + "_模拟演练"
        document.title = pName;
        $("#projectTitle").text(pName);
        setLineState(false, "");

        //常规使用 - 普通图片上传
        upload.render({
            elem: '#upImg'
            , url: uploadFileUrl //上传接口。
            , before: function (obj) {
                //预读本地文件示例，不支持ie8
                obj.preview(function (index, file, result) {
                    $('#demo1').attr('src', result); //图片链接（base64）
                });

                element.progress('demo', '0%'); //进度条复位
                layer.msg('上传中', { icon: 16, time: 0 });
            }
            , done: function (msgResult) {
                //如果上传失败
                if (msgResult.msgCode == 1) {
                    openPictureLayer("图片图层", "images/imgfiles/" + msgResult.data);
                    var spatial_xmin = document.getElementById("spatial_xmin");
                    var spatial_ymin = document.getElementById("spatial_ymin");
                    var spatial_xmax = document.getElementById("spatial_xmax");
                    var spatial_ymax = document.getElementById("spatial_ymax");
                    var img_visible = document.getElementById("img_visible");

                    spatial_xmin.value = imgCameraJson.extent.xmin;
                    spatial_ymin.value = imgCameraJson.extent.ymin;
                    spatial_xmax.value = imgCameraJson.extent.xmax;
                    spatial_ymax.value = imgCameraJson.extent.ymax;
                    img_visible.checked = imgCameraJson.visible;
                    //openPictureLayer("图片图层", "images/1.jpg");

                    var tmpjson = {
                        "infoType": "img",
                        "eventInfo": jqJson,
                        "userInfo": [
                            userJson
                        ],
                        "drawInfo": [],
                        "imgInfo": {
                            imgUrl: "images/imgfiles/" + msgResult.data,
                            imgName: msgResult.data,
                            minx: imgCameraJson.extent.xmin,
                            miny: imgCameraJson.extent.ymin,
                            maxx: imgCameraJson.extent.xmax,
                            maxy: imgCameraJson.extent.ymax,
                            belongUser: userJson.user
                        }
                    };
                    if (ws != null && ws.isOnLine == true) {
                        ws.send(tmpjson);
                    }
                }
                else {
                    layer.msg('图片上传失败');
                }

                //上传成功的一些操作
                //……
                $('#demoText').html(''); //置空上传失败的状态
            }
            , error: function () {
                //演示失败状态，并实现重传
                var demoText = $('#demoText');
                demoText.html('<span style="color: #FF5722;">图片上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
                demoText.find('.demo-reload').on('click', function () {
                    uploadInst.upload();
                });
            }
            //进度条
            , progress: function (n, elem, e) {
                element.progress('demo', n + '%'); //可配合 layui 进度条元素使用

                if (n == 100) {
                    layer.msg('上传完毕', { icon: 1 });
                }
            }
        });

        //图层列表
        tree.render({
            elem: '#treeLayer'
            , data: layerJsons
            , showCheckbox: true
        });

        label_show.disabled = true;
        $("input[name='label_align'][value='left']").attr("disabled", true);
        $("input[name='label_align'][value='center']").attr("disabled", true);
        $("input[name='label_align'][value='right']").attr("disabled", true);

        $("#ul_fill_url").css("display", "none");

        //物体比例
        slider.render({
            elem: '#wt_hw'
            , min: 10 //最小值
            , max: 300 //最大值
            , value: 100
            , step: 10
            , input: true,
            disabled: true
        });

        //物体角度
        slider.render({
            elem: '#wt_angle'
            , min: -360 //最小值
            , max: 360 //最大值
            , value: 0
            , step: 10
            , input: true,
            disabled: true
        });

        //边线宽度
        slider.render({
            elem: '#border_width'
            , min: 0 //最小值
            , max: 50 //最大值
            , input: true,
            disabled: true
        });

        //注记大小
        slider.render({
            elem: '#label_size'
            , min: 1 //最小值
            , max: 50 //最大值
            , input: true,
            disabled: true
        });
        //注记颜色
        $("#label_bcolor").css("display", "none");
        colorpicker.render({
            elem: '#label_bcolor',  //绑定元素
            color: 'white',
            format: 'rgb',
            alpha: true,
            predefine: true,
            colors: ['#F00', '#0F0', '#00F', 'rgb(255, 69, 0)', 'rgba(255, 69, 0, 0.5)'],
            disabled: true
            // size: 'xs',
        });
        //图形边框颜色
        $("#border_color").css("display", "none");
        colorpicker.render({
            elem: '#border_color',  //绑定元素
            color: 'white',
            format: 'rgb',
            alpha: true,
            predefine: true,
            colors: ['#F00', '#0F0', '#00F', 'rgb(255, 69, 0)', 'rgba(255, 69, 0, 0.5)'],
            size: 'xs',
            disabled: true
        });

        //图形填充颜色
        $("#fill_color").css("display", "none");
        colorpicker.render({
            elem: '#fill_color',  //绑定元素
            color: 'white',
            format: 'rgb',
            alpha: true,
            predefine: true,
            colors: ['#F00', '#0F0', '#00F', 'rgb(255, 69, 0)', 'rgba(255, 69, 0, 0.5)'],
            size: 'xs',
            disabled: true
        });

    });

    require(["esri/Map", "esri/Basemap", "esri/config", "esri/geometry/SpatialReference", "esri/geometry/Extent", "esri/Ground", "esri/views/MapView", "esri/views/SceneView", "esri/layers/ElevationLayer", "esri/layers/ImageryTileLayer", "esri/layers/ImageryLayer", "esri/layers/WebTileLayer", "esri/layers/SceneLayer", "esri/layers/GraphicsLayer", "esri/layers/TileLayer", "esri/layers/MapImageLayer", "esri/layers/FeatureLayer", "esri/layers/support/LabelClass", 'esri/layers/support/TileInfo', "esri/Graphic", "esri/widgets/Sketch/SketchViewModel", "esri/core/promiseUtils", "esri/widgets/Compass"],
        (Map, Basemap, esriConfig, SpatialReference, Extent, Ground, MapView, SceneView, ElevationLayer, ImageryTileLayer, ImageryLayer, WebTileLayer, SceneLayer, GraphicsLayer, TileLayer, MapImageLayer, FeatureLayer, LabelClass, TileInfo, Graphic, SketchViewModel, promiseUtils, Compass) => {
            //必须配置，不然会报跨域
            //esriConfig.request.proxyUrl = "http://127.0.0.1/Proxy/proxy.ashx";//其中proxyUrl是你部署到IIS下的路径即可。
            esriConfig.fontsUrl = "./js/arcgis_js_api/4.25/arcgisFont";
            map = new Map({
                //layers: [imglayer]
            });
            view = new MapView({
                container: "viewDiv",
                map: map,
                spatialReference: { wkid: 102100 },
                center: projectMapCenter || [118.06, 24.47],
                zoom: 18,
                constraints: {
                    rotationEnabled: false,
                    maxZoom: 18,
                    minZoom: 5
                }
            });
            let compass = new Compass({
                view: view
            });

            // adds the compass to the top left corner of the MapView
            view.ui.add(compass, "top-left");

            //半径缓冲区图层
            bufferlayer = new GraphicsLayer({
                id: 9999,
            });
            map.add(bufferlayer);

            //绘制对象图层
            glayer = new GraphicsLayer({
                id: 10001
            });
            map.add(glayer);
            //注记图层
            zjlayer = new GraphicsLayer({
                id: 10002
            });
            map.add(zjlayer);
            //临时图层
            tmplayer = new GraphicsLayer({
                id: 10003,
                hasz: false
            });
            map.add(tmplayer);

            // //载入场景列表
            // loadScenes();
            //载入图层列表
            loadLayers();
            //载入模型列表
            // loadModels();
            //载入填充图案列表
            loadFillImg();

            //*********arcgisdraw中初始化，传入地图、视图、临时图层、绘制对象图层及注记图层************
            init(map, view, tmplayer, glayer, zjlayer);

            //*********arcgisdraw中选中、编辑完成后，触发回调，回调中获取标绘后的要素信息************
            initEdit(function (data) {
                setAttData(data);
            });

            /**
            * 根据类别，显示隐藏相关属性
           */
            displayAttData = function (wt_type, user) {
                $("#tr_wt_angle").css("display", "none");
                $("#tr_wt_hw").css("display", "none");

                $("#tr_spatial_length").css("display", "none");
                $("#tr_spatial_area").css("display", "none");

                $("#tr_border_width").css("display", "none");
                $("#tr_border_color").css("display", "none");

                $("#tr_fill_url").css("display", "none");
                $("#tr_fill_url_width").css("display", "none");
                $("#tr_fill_color").css("display", "none");

                if (wt_type == "img") {
                    $("#tr_wt_hw").css("display", "");
                    $("#tr_wt_angle").css("display", "");
                }
                else if (wt_type == "point") {
                }
                else if (wt_type == 'freepolyline' || wt_type == "polyline" || wt_type == 'arrow' || wt_type == 'waterPipe') {
                    $("#tr_spatial_length").css("display", "");
                    $("#tr_border_width").css("display", "");
                    // 2024-03-04 tmj 原有禁用判定改为启用判定
                    if (((userJson.r == 2 && user != "") || (userJson.r == 1 && userJson.user == user)) && ws.isOnLine == true) {
                        $("#tr_border_color").css("display", "");
                        $("#tr_fill_url").css("display", "");
                        $("#tr_fill_url_width").css("display", "");
                        $("#tr_fill_color").css("display", "");
                    }
                    spatial_lon.readOnly = true;
                    spatial_lat.readOnly = true;
                    // 2024-03-04 tmj 改掉原有判定方式
                    // if (userJson.r != 1 || userJson.user != user || ws.isOnLine != true) {
                    //     $("#tr_border_color").css("display", "none");
                    //     $("#tr_fill_url").css("display", "none");
                    //     $("#tr_fill_url_width").css("display", "none");
                    //     $("#tr_fill_color").css("display", "none");
                    // }
                }
                else if (wt_type == 'freepolygon' || wt_type == "polygon" || wt_type == "rectangle" || wt_type == "circle" || wt_type == 'tailedsquadcombat' || wt_type == 'sector' || wt_type == 'doublearrow' || wt_type == 'finearrow' || wt_type == 'squadcombat' || wt_type == 'attackarrow' || wt_type == 'tailedattackarrow' || wt_type == 'gatheringplace' || wt_type == 'closedcurve') {
                    $("#tr_spatial_length").css("display", "");
                    $("#tr_spatial_area").css("display", "");
                    $("#tr_border_width").css("display", "");
                    // 2024-03-04 tmj 原有禁用判定改为启用判定
                    if (((userJson.r == 2 && user != "") || (userJson.r == 1 && userJson.user == user)) && ws.isOnLine == true) {
                        $("#tr_border_color").css("display", "");
                        $("#tr_fill_url").css("display", "");
                        $("#tr_fill_url_width").css("display", "");
                        $("#tr_fill_color").css("display", "");
                    }
                    spatial_lon.readOnly = true;
                    spatial_lat.readOnly = true;
                    // 2024-03-04 tmj 改掉原有判定方式
                    // if (userJson.r != 1 || userJson.user != user || ws.isOnLine != true) {
                    //     $("#tr_border_color").css("display", "none");
                    //     $("#tr_fill_url").css("display", "none");
                    //     $("#tr_fill_url_width").css("display", "none");
                    //     $("#tr_fill_color").css("display", "none");
                    // }
                }
            }

            setAttData = function (data) {

                var isDisabled = true;
                //var isReadonly = true;
                // 2024-03-04 tmj 增加全编辑权限判定 //if (data != null && userJson.r == 1 && userJson.user == data.user && ws.isOnLine == true) {
                if (data != null && ((userJson.r == 2 && data.user != "") || (userJson.r == 1 && userJson.user == data.user)) && ws.isOnLine == true) {
                    isDisabled = false;
                    $("#border_color").css("display", "inline");
                    $("#label_bcolor").css("display", "inline");
                    $("#fill_color").css("display", "inline");
                    $("#ul_fill_url").css("display", "flex");
                    $("#btn_delGrap").removeClass("layui-btn-disabled");
                    document.getElementById("btn_delGrap").disabled = isDisabled;
                }
                else {
                    $("#label_bcolor").css("display","none");
                    $("#border_color").css("display", "none");
                    $("#fill_color").css("display", "none");
                    $("#ul_fill_url").css("display", "none");
                    $("#btn_delGrap").addClass("layui-btn-disabled");
                    document.getElementById("btn_delGrap").disabled = isDisabled;
                }
                //根据用户设置是否只读
                wt_name.readOnly = isDisabled;
                wt_hw.readOnly = isDisabled;
                wt_length.readOnly = isDisabled;
                spatial_lon.readOnly = isDisabled;
                spatial_lat.readOnly = isDisabled;
                // spatial_height.readOnly = isDisabled;
                // spatial_heading.readOnly = isDisabled;
                // spatial_picth.readOnly = isDisabled;
                // spatial_roll.readOnly = isDisabled;
                //img_url.readOnly = isDisabled;
                label_show.disabled = isDisabled;
                label_height.readOnly = isDisabled;

                //label_height.readOnly = isDisabled;
                fill_url_width.readOnly = isDisabled;
                fill_url_height.readOnly = isDisabled;
                $("input[name='label_align'][value='left']").prop("disabled", isDisabled);
                $("input[name='label_align'][value='center']").prop("disabled", isDisabled);
                $("input[name='label_align'][value='right']").prop("disabled", isDisabled);

                if (data != null) {
                    displayAttData(data.wt_type, data.user);
                    user_name.value = data.user_name;
                    user_time.value = data.user_time;
                    wt_id.value = data.wt_id;
                    wt_name.value = data.wt_name;
                    wt_length.value = data.wt_length;
                    wt_hw.value = data.wt_hw;
                    spatial_length.value = data.spatial_length;
                    spatial_area.value = data.spatial_area;
                    spatial_lon.value = data.spatial_lon;
                    spatial_lat.value = data.spatial_lat;
                    img_url.value = data.img_url;
                    label_show.checked = data.label_show;
                    label_height.value = data.label_height;

                    fill_url.value = data.fill_url;
                    setfillimg(data.fill_url);//设置填充图案
                    fill_url_width.value = data.fill_url_width;
                    fill_url_height.value = data.fill_url_height;

                    //注记对齐方式
                    if (data.label_align == 'left') {
                        $("input[name='label_align'][value='left']").prop("checked", true);
                    }
                    else if (data.label_align == 'center') {
                        $("input[name='label_align'][value='center']").prop("checked", true);
                    }
                    else {
                        $("input[name='label_align'][value='right']").prop("checked", true);
                    }

                    //物体比例
                    $("#wt_hw").val((data.wt_width * 100 / 32).toFixed(0));
                    slider.render({
                        elem: '#wt_hw'
                        , min: 10 //最小值
                        , max: 300 //最大值
                        , step: 10
                        , value: (data.wt_width * 100 / 32).toFixed(0)
                        , input: true
                        , disabled: isDisabled
                        , change: function (value) {
                            $('#wt_hw').val(value);
                            txtKeyup();
                        }
                    });

                    //物体角度
                    $("#wt_angle").val(data.wt_angle);
                    slider.render({
                        elem: '#wt_angle'
                        , min: -360 //最小值
                        , max: 360 //最大值
                        , step: 10
                        , value: data.wt_angle
                        , input: true
                        , disabled: isDisabled
                        , change: function (value) {
                            $('#wt_angle').val(value);
                            txtKeyup();
                        }
                    });

                    //边线宽度
                    $("#border_width").val(data.border_width);
                    slider.render({
                        elem: '#border_width'
                        , min: 0 //最小值
                        , max: 50 //最大值
                        , value: data.border_width
                        , input: true
                        , disabled: isDisabled
                        , change: function (value) {
                            $('#border_width').val(value);
                            txtKeyup();
                        }
                    });

                    //注记大小
                    $("#label_size").val(data.label_size);
                    slider.render({
                        elem: '#label_size'
                        , min: 1 //最小值
                        , max: 50 //最大值
                        , value: data.label_size
                        , input: true
                        , disabled: isDisabled
                        , change: function (value) {
                            $('#label_size').val(value);
                            txtKeyup();
                        }
                    });
                    //注记颜色
                    $("#label_bcolor").val(data.label_bcolor ? data.label_bcolor : userJson.color);
                    colorpicker.render({
                        elem: '#label_bcolor',  //绑定元素
                        color: data.label_bcolor.toString(),
                        format: 'rgb',
                        alpha: true,
                        predefine: true,
                        colors: ['#F00', '#0F0', '#00F', 'rgb(255, 69, 0)', 'rgba(255, 69, 0, 0.5)'],
                        size: 'xs',
                        done: function (color) {
                            $("#label_bcolor").val(color);
                            txtKeyup();
                        }
                    });

                    //对象边框颜色
                    $("#border_color").val(data.border_color);
                    colorpicker.render({
                        elem: '#border_color',  //绑定元素
                        color: data.border_color.toString(),
                        format: 'rgb',
                        alpha: true,
                        predefine: true,
                        colors: ['#F00', '#0F0', '#00F', 'rgb(255, 69, 0)', 'rgba(255, 69, 0, 0.5)'],
                        size: 'xs',
                        done: function (color) {
                            $("#border_color").val(color);
                            txtKeyup();
                        }
                    });

                    //填充颜色
                    $("#fill_color").val(data.fill_color);
                    colorpicker.render({
                        elem: '#fill_color',  //绑定元素
                        color: data.fill_color.toString(),
                        format: 'rgb',
                        alpha: true,
                        predefine: true,
                        colors: ['#F00', '#0F0', '#00F', 'rgb(255, 69, 0)', 'rgba(255, 69, 0, 0.5)'],
                        size: 'xs',
                        done: function (color) {
                            $("#fill_color").val(color);
                            txtKeyup();
                        }
                    });
                }
                else {
                    user_name.value = "";
                    user_time.value = "";
                    wt_id.value = "";
                    wt_name.value = "";
                    wt_length.value = "";
                    wt_hw.value = "";
                    spatial_length.value = "";
                    spatial_area.value = "";
                    spatial_lon.value = "";
                    spatial_lat.value = "";
                    img_url.value = "";
                    label_show.checked = false;
                    label_height.value = "";

                    fill_url.value = "";
                    fill_url_width.value = "";
                    fill_url_height.value = "";

                    $("input[name='label_align'][value='center']").prop("checked", true);
                    $("#wt_angle").val(0);
                    $("#wt_hw").val(10);
                    //物体比例
                    slider.render({
                        elem: '#wt_hw'
                        , min: 10 //最小值
                        , max: 100 //最大值
                        , value: 10
                        , step: 10
                        , input: true
                        , disabled: isDisabled
                    });
                    //物体角度
                    slider.render({
                        elem: '#wt_angle'
                        , min: -360 //最小值
                        , max: 360 //最大值
                        , value: 0
                        , step: 10
                        , input: true
                        , disabled: isDisabled
                    });
                    $("#border_width").val(0);
                    //边线宽度
                    slider.render({
                        elem: '#border_width'
                        , min: 0 //最小值
                        , max: 50 //最大值
                        , input: true
                        , disabled: isDisabled
                    });
                    $("#label_size").val(1);
                    //注记大小
                    slider.render({
                        elem: '#label_size'
                        , min: 1 //最小值
                        , max: 50 //最大值
                        , input: true
                        , disabled: isDisabled
                    });
                    //图形边框颜色
                    $("#border_color").val("");
                    colorpicker.render({
                        elem: '#border_color',  //绑定元素
                        color: 'white',
                        format: 'rgb',
                        alpha: true,
                        predefine: true,
                        colors: ['#F00', '#0F0', '#00F', 'rgb(255, 69, 0)', 'rgba(255, 69, 0, 0.5)'],
                        size: 'xs'
                    });

                    //图形填充颜色
                    $("#fill_color").val("");
                    colorpicker.render({
                        elem: '#fill_color',  //绑定元素
                        color: 'white',
                        format: 'rgb',
                        alpha: true,
                        predefine: true,
                        colors: ['#F00', '#0F0', '#00F', 'rgb(255, 69, 0)', 'rgba(255, 69, 0, 0.5)'],
                        size: 'xs',
                    });
                }
            }

            //更改属性后，需要更新地图上模型图标
            txtKeyup = function (state) {
                // 2024-03-01 tmj 编辑权限判定//if (user_name.value != "" && user_name.value == userJson.name) {
                if (user_name.value != "" && (user_name.value == userJson.name || (userJson.r == 2 && user_name.value != ""))) {
                    var time = getNowTime();
                    var data = {};
                    data.user = userJson.user;
                    data.user_name = userJson.name;
                    data.user_time = time;
                    $("#user_time").val(time);
                    data.wt_id = wt_id.value;
                    data.wt_name = wt_name.value;
                    data.wt_length = wt_length.value;
                    data.wt_hw = wt_hw.value;
                    data.wt_height = $("#wt_hw").val() * 32 * 0.01;
                    data.wt_width = $("#wt_hw").val() * 32 * 0.01;
                    data.wt_angle = $("#wt_angle").val();
                    data.spatial_lon = spatial_lon.value;
                    data.spatial_lat = spatial_lat.value;
                    data.img_url = img_url.value;
                    data.label_show = label_show.checked;
                    data.label_height = label_height.value;
                    data.label_bcolor = $("#label_bcolor").val();
                    data.label_size = $("#label_size").val();
                    data.border_color = $("#border_color").val();
                    data.border_width = $("#border_width").val();
                    data.fill_color = $("#fill_color").val();
                    data.label_align = $("input[name='label_align']:checked").val();
                    data.fill_url = getfillimg();
                    data.fill_url_width = fill_url_width.value;
                    data.fill_url_height = fill_url_height.value;
                    //*********arcgisdraw中更新模型图标*************
                    keyUpdata(data, state);
                }
            }

            //添加接警点
            addEventPoint(jqJson.lon, jqJson.lat);

            //连接服务器
            socketConnect = function () {
                ws = new SocketInfo({
                    url: host + "/ws",
                    user: userJson.user,
                    userUrl: "/user/" + userJson.user + "/plotMessage/" + jqJson.id,
                    subscribeUrl: "/plotMessage/" + jqJson.id,
                    sendUrl: "/pushPlotMessage/" + jqJson.id,
                    timeout: 10000,
                    connectMessage: (state) => {
                        // console.log("连接成功！！！", state);
                        setLineState(true, "");
                        socketSend();
                    },
                    userMessage: (res) => {
                        // console.log("一对一");

                        var json = JSON.parse(res.body);

                        if (json.infoType == "all") {
                            clearTabLegend();
                            glayer.removeAll();
                            zjlayer.removeAll();
                            //添加接警点
                            var eventInfo = json.eventInfo;
                            jqJson.lon = eventInfo.lon * 1;
                            jqJson.lat = eventInfo.lat * 1;
                            jqJson.content = eventInfo.content;
                            addEventPoint(jqJson.lon, jqJson.lat);
                            var userInfo = json.userInfo;
                            var drawInfo = json.drawInfo;

                            //遍历获取用户
                            for (let i = 0; i < userInfo.length; i++) {
                                const user = userInfo[i];
                                //设置自己账号的标绘颜色
                                if (userJson.user == user.user) {
                                    userJson.color = user.color;
                                }
                                setLegend(user.user, user.name, user.status, user.color);
                                setTabLegend(user.user, user.name, user.status, user.color);
                            }

                            //遍历获取要素
                            drawInfo.forEach(Json => {
                                if (Json.featureGraphic != "") {
                                    var featJson = JSON.parse(Json.featureGraphic);//要素JSON
                                    var labelJson = "";//注记JSON
                                    if (Json.labelGraphic != "")
                                        labelJson = JSON.parse(Json.labelGraphic);

                                    if (Json.id != "jqzhd") {
                                        var grap = addGraphicMethod(featJson, labelJson, true);//直接添加进图层
                                        //jqGraphic = grap;
                                    }
                                }
                            });
                            //根据地图级别，设置图标显示大小
                            viewZoom(view.zoom, true);
                            // if (jqGraphic == null) {
                            //     addEventPoint(jqJson.lon, jqJson.lat);
                            // }
                            var imgInfo = json.imgInfo;
                            if (imgInfo != null && imgInfo.imgUrl != null && imgInfo.minx != null) {
                                imgCameraJson.id = "10000";
                                imgCameraJson.title = "图片图层";
                                imgCameraJson.type = "custom";
                                imgCameraJson.extent = {
                                    xmin: imgInfo.minx,
                                    ymin: imgInfo.miny,
                                    xmax: imgInfo.maxx,
                                    ymax: imgInfo.maxy,
                                    spatialReference: { wkid: 4326 }
                                };
                                imgCameraJson.url = imgInfo.imgUrl;
                                //显示隐藏根据当前图片显隐值，默认是显示
                                imgCameraJson.visible = document.getElementById("img_visible").checked;
                                loadPictureLayer(imgCameraJson);
                                var spatial_xmin = document.getElementById("spatial_xmin");
                                var spatial_ymin = document.getElementById("spatial_ymin");
                                var spatial_xmax = document.getElementById("spatial_xmax");
                                var spatial_ymax = document.getElementById("spatial_ymax");
                                var img_visible = document.getElementById("img_visible");

                                spatial_xmin.value = imgCameraJson.extent.xmin;
                                spatial_ymin.value = imgCameraJson.extent.ymin;
                                spatial_xmax.value = imgCameraJson.extent.xmax;
                                spatial_ymax.value = imgCameraJson.extent.ymax;
                                //img_visible.checked = imgCameraJson.visible;
                            }

                            //定位到接警点，防止断线重连后反复定位
                            if (isGoJQ == false) {
                                gotoAngle(view.heading, view.tilt, { longitude: jqJson.lon, latitude: jqJson.lat }, 18, null, function (title) { });
                                isGoJQ = true;
                            }
                            if (isLoadCopy == false) {
                                //载入副本列表
                                loadScenes();
                                isLoadCopy = true;
                            }
                            if (isLoadImg == false) {
                                //载入截图列表
                                loadSceneImg();
                                isLoadImg = true;
                            }
                        }
                    },
                    // 订阅信息的返回
                    handleMessage: (res) => {
                        // console.log("广播消息");
                        var json = JSON.parse(res.body);
                        if (json.infoType == "live") {
                            var userInfo = json.userInfo;
                            debugger
                            for (let i = 0; i < userInfo.length; i++) {
                                const user = userInfo[i];

                                //设置自己账号的标绘颜色
                                if (userJson.user == user.user) {
                                    userJson.color = user.color;
                                    // 其他端新进入页面角色可能会变化设置创建角色
                                    userJson.createRole = user.name.split("】【")[0].replace("【", "");
                                }
                                setLegend(user.user, user.name, user.status, user.color);
                                setTabLegend(user.user, user.name, user.status, user.color);

                                // 只有首次上线或之前显示过下线通知的用户才显示上线通知
                                if (!shownOnlineNotifications[user.user]) {
                                    layer.msg(user.name + '上线', { icon: 1, offset: "rb", area: ['290px', '60px'] });
                                    shownOnlineNotifications[user.user] = true;
                                    onlineUserHash[user.user] = user.name;
                                }
                            }

                            //定位到接警点，防止断线重连后反复定位
                            if (isGoJQ == false) {
                                gotoAngle(view.heading, view.tilt, { longitude: jqJson.lon, latitude: jqJson.lat }, 19, null, function (title) { });
                                isGoJQ = true;
                            }
                            if (isLoadCopy == false) {
                                //载入副本列表
                                loadScenes();
                                isLoadCopy = true;
                            }
                            if (isLoadImg == false) {
                                //载入截图列表
                                loadSceneImg();
                                isLoadImg = true;
                            }
                        }
                        else if (json.infoType == "update") {
                            // console.log("update", json);
                            var drawInfo = json.drawInfo;
                            //遍历
                            drawInfo.forEach(Json => {
                                var featJson = JSON.parse(Json.featureGraphic);//要素JSON
                                var labelJson = JSON.parse(Json.labelGraphic);//注记JSON
                                addGraphicMethod(featJson, labelJson, false);//判定是否存在后处理
                            });
                        }
                        else if (json.infoType == "delete") {
                            // console.log("delete", json);
                            var drawInfo = json.drawInfo;
                            //遍历
                            drawInfo.forEach(Json => {
                                delGraphicMethod(Json.id);
                            });
                        }
                        else if (json.infoType == "out") {
                            console.log("out", json);
                            var userInfo = json.userInfo;
                            for (let i = 0; i < userInfo.length; i++) {
                                const user = userInfo[i];
                                //自己其他客户端下线，自己本地被动不下线
                                if (user.user != userJson.user) {
                                    var name = onlineUserHash[user.user];
                                    // 标记该用户已下线，这样下次上线时才会显示通知
                                    if (shownOnlineNotifications[user.user]) {
                                        shownOnlineNotifications[user.user] = false;
                                        setOutLegend(user.user);
                                        layer.msg(name + '下线', { icon: 0, offset: "rb", area: ['290px', '60px'] });
                                    }
                                }
                            }
                        }
                        else if (json.infoType == "img") {
                            var spatial_xmin = document.getElementById("spatial_xmin");
                            var spatial_ymin = document.getElementById("spatial_ymin");
                            var spatial_xmax = document.getElementById("spatial_xmax");
                            var spatial_ymax = document.getElementById("spatial_ymax");
                            var img_visible = document.getElementById("img_visible");
                            var imgInfo = json.imgInfo;
                            if (imgInfo.imgUrl != null && imgInfo.minx != null) {
                                if (imgCameraJson != null && imgCameraJson.url != imgInfo.imgUrl) {
                                    imgCameraJson.id = "10000";
                                    imgCameraJson.title = "图片图层";
                                    imgCameraJson.type = "custom";
                                    imgCameraJson.extent = {
                                        xmin: imgInfo.minx,
                                        ymin: imgInfo.miny,
                                        xmax: imgInfo.maxx,
                                        ymax: imgInfo.maxy,
                                        spatialReference: { wkid: 4326 }
                                    };
                                    imgCameraJson.url = imgInfo.imgUrl;
                                    imgCameraJson.visible = document.getElementById("img_visible").checked;
                                    loadPictureLayer(imgCameraJson);


                                    spatial_xmin.value = imgCameraJson.extent.xmin;
                                    spatial_ymin.value = imgCameraJson.extent.ymin;
                                    spatial_xmax.value = imgCameraJson.extent.xmax;
                                    spatial_ymax.value = imgCameraJson.extent.ymax;
                                    //img_visible.checked = imgCameraJson.visible;
                                }
                                else if (imgCameraJson.url == imgInfo.imgUrl) {
                                    imgCameraJson.extent = {
                                        xmin: imgInfo.minx,
                                        ymin: imgInfo.miny,
                                        xmax: imgInfo.maxx,
                                        ymax: imgInfo.maxy,
                                        spatialReference: { wkid: 4326 }
                                    };
                                    spatial_xmin.value = imgCameraJson.extent.xmin;
                                    spatial_ymin.value = imgCameraJson.extent.ymin;
                                    spatial_xmax.value = imgCameraJson.extent.xmax;
                                    spatial_ymax.value = imgCameraJson.extent.ymax;
                                    ImageOverlayLayer.refresh();
                                }
                            }
                        }
                        else if (json.infoType == "event") {
                            //console.log("event", json);
                            var eventInfo = json.eventInfo;
                            jqJson.lon = eventInfo.lon * 1;
                            jqJson.lat = eventInfo.lat * 1;
                            jqJson.content = eventInfo.content;
                            updateZHD(eventInfo.lon, eventInfo.lat);
                            layer.msg("当前警情位置已变更,新坐标[X:" + eventInfo.lon + ",Y:" + eventInfo.lat + "]");
                        }
                        else if (json.infoType == "details") {
                            console.log("details", json);
                            var eventDetails = json.eventDetails;

                            // Update timeline with event details
                            if (eventDetails && eventDetails.length > 0) {
                                updateTimeline(eventDetails);
                                addPublishInfo(eventDetails)
                            }
                        }
                    },
                    errorMessage: (error) => {
                        setLineState(false, "(" + ws.lineNum + ")");
                    }
                });
                ws.connect();

            }

            // 发送信息,测试
            socketSend = function () {
                var tmpjson = {
                    "infoType": "live",
                    "eventInfo": jqJson,
                    "userInfo": [
                        userJson
                    ],
                    "drawInfo": []
                };
                if (ws != null && ws.isOnLine == true)
                    ws.send(tmpjson);
            }

            //删除绘制对象
            deleteGraphic = function () {
                if (curGraphic != null) {
                    var wtid = curGraphic.attributes["oid"];
                    var wtmc = curGraphic.attributes["wtmc"];
                    var wtdz = curGraphic.attributes["wtdz"];
                    var content =
                        "名称【" +
                        wtmc +
                        "】，经纬度[" +
                        selectData.spatial_lon.toFixed(6)
                        +
                        "," +
                        selectData.spatial_lat.toFixed(6) +
                        "]";
                    var operateObjName = curGraphic.attributes['operate']
                    var del = delGraphicMethod(wtid);
                    if (del == true) {
                        sketchCancel();
                        setAttData(null);
                        var tmpjson = {
                            "infoType": "delete",
                            "eventInfo": jqJson,
                            "userInfo": [
                                userJson
                            ],
                            "drawInfo": [{ id: wtid, featureGraphic: "", labelGraphic: "" }]
                        };
                        if (ws != null && ws.isOnLine == true) {
                            ws.send(tmpjson);
                            if (wtmc != "")
                                layer.msg("删除名称为【" + wtmc + "】的物体成功！");
                            else
                                layer.msg("删除标识为【" + wtid + "】的物体成功！");
                        }

                        var eventDetailId = jqJson.id.replace("（2D）", "");
                        // 军标图形直接取wtdz, 其它从图片地址抽出操作对象名称
                        // var operateObjName = wtdz;
                        // if (wtdz.indexOf(".png") != -1) {
                        //     operateObjName = wtdz
                        //         ? wtdz.substring(
                        //             wtdz.lastIndexOf("/") + 1,
                        //             wtdz.lastIndexOf(".png")
                        //         )
                        //         : "";
                        // }
                        var eventDetailJson = {
                            infoType: "details",
                            eventInfo: jqJson,
                            eventDetails: [
                                {
                                    eventId: eventDetailId,
                                    operateName: operateObjName,
                                    eventContent: content,
                                    eventType: "删除",
                                    createUser: userJson.createUser,
                                    createRole: userJson.createRole,
                                },
                            ],
                        };
                        if (ws != null && ws.isOnLine == true) ws.send(eventDetailJson);
                        isAdd = false;
                        isUpdate = false;
                        isDel = false;
                        selectData = {
                            user: "",
                            user_name: "",
                            user_time: "",
                            wt_id: "",
                            wt_name: "",
                            wt_length: 0,//长
                            wt_hw: 100,//宽高比
                            spatial_length: 0,//周长
                            spatial_area: 0,//面积
                            spatial_lon: "",
                            spatial_lat: "",
                            spatial_height: "",
                            spatial_heading: "",//偏航角
                            spatial_picth: "",//俯仰角
                            spatial_roll: "",//翻滚角
                            img_url: "",
                            label_show: true,//注记显示
                            label_height: 0,//注记高度
                            label_bcolor: "",//注记背景颜色
                            label_size: 10,//注记大小
                            label_align: "center",//注记对齐方式
                            border_width: 5,//图形边线宽度
                            border_color: "",//图形边线颜色
                            fill_color: ""//图形填充颜色
                        };
                        curGraphic = null;
                    }
                }

            }

            centerToEvent = function () {

                if (jqJson.lon != "" && jqJson.lat != "") {
                    if (jqGraphic != null)
                        sketchViewModel.update(jqGraphic);
                    //heading, tilt, centerPoint, zoom, title, fun
                    gotoAngle(view.heading, view.tilt, { longitude: jqJson.lon, latitude: jqJson.lat }, 19, null, function (title) { });
                }
            }

            //用户认证
            initAuthorize(function (uJson) {
                userJson.createUser = userJson.name;
                userJson.createRole = jqJson.role;
                userJson.name = "【" + jqJson.role + "】【" + userJson.name + "】";
                getEventDetail();
                socketConnect();
                queryModelTree()
            }, function (err) { });
            //socketConnect();

        });

    /**
     * 测试，加载图片到地图
    */
    function openImg() {
        openPictureLayer("图片图层", "images/1.jpg");
        var spatial_xmin = document.getElementById("spatial_xmin");
        var spatial_ymin = document.getElementById("spatial_ymin");
        var spatial_xmax = document.getElementById("spatial_xmax");
        var spatial_ymax = document.getElementById("spatial_ymax");
        var img_visible = document.getElementById("img_visible");

        spatial_xmin.value = imgCameraJson.extent.xmin;
        spatial_ymin.value = imgCameraJson.extent.ymin;
        spatial_xmax.value = imgCameraJson.extent.xmax;
        spatial_ymax.value = imgCameraJson.extent.ymax;
        img_visible.checked = imgCameraJson.visible;
    }

    function filimgClick(evt) {
        if (evt.className == "li-fill-Img li-fill-select") {
            $(".li-fill-select").removeClass("li-fill-select");
        }
        else {
            $(".li-fill-select").removeClass("li-fill-select");
            evt.className = "li-fill-Img li-fill-select";
        }
        txtKeyup();
    }

    function getfillimg() {
        if ($(".li-fill-select").length > 0)
            return $(".li-fill-select").attr("fillUrl");
        else
            return "";
    }

    function setfillimg(fillImgUrl) {
        if (fillImgUrl != "" && fillImgJsons != undefined) {
            try {
                // var name = "";
                // var arr = fillImgUrl.split("\\");
                // var fileName = arr[arr.length - 1];
                // var k = fileName.lastIndexOf(".");
                // name = fileName.substr(0,k);
                for (let i = 0; i < fillImgJsons.length; i++) {
                    const json = fillImgJsons[i];
                    if (fillImgUrl.indexOf(json.fillurl) > -1) {
                        $(".li-fill-select").removeClass("li-fill-select");
                        $("#" + json.id).addClass("li-fill-select");
                        break;
                    }
                }
            }
            catch (ex) { }
        }
        else {
            $(".li-fill-select").removeClass("li-fill-select");
        }
    }

    /**
    * 载入填充图案列表
    */
    function loadFillImg() {
        $("#ul_fill_url").empty();
        //调用截图列表接口
        $.ajax({
            url: "data/fillimg.json", success: function (resultJsons) {
                if (resultJsons != null) {
                    fillImgJsons = resultJsons;
                    resultJsons.forEach(json => {
                        addliFillImg(json.id, json.slturl, json.fillurl, json.width, json.height);
                    });
                }
            }
        });
    }

    /**
     * 打开三维编辑页面
    */
    var page3d = null;
    function open3D() {
        page3d = window.open("index.html", "3d");
        if (page3d != null) {
            page3d.focus();
        }
    }

    /**
     * 获取演练时间轴
     */
    function formatEventContent(content) {
        if (!content) return "";

        // 按逗号分隔内容
        var parts = content.split("，");

        // 如果只有一部分或没有逗号，直接返回原内容
        if (parts.length <= 1) return content;

        // 将每部分用<br>连接起来
        return parts.join("<br>");
    }

    /**
     * 获取演练时间轴
     */
    function getEventDetail() {
        var detailUrl = host + "/xf/event/detail/list/" + jqJson.id.replace("（2D）", "");
        $.ajax({
            url: detailUrl,
            success: function (result) {
                if (result.msgSuccess && result.data.length > 0) {
                    updateTimeline(result.data);
                    handleInitPublishInfo(result.data)
                }
            }
        });
    }

    /**
     * 更新演练时间轴
     * @param {Array} eventDetails 事件详情数组
     */
    function updateTimeline(eventDetails) {
        // 清空现有时间轴内容，包括"暂无数据"提示
        $(".timeline-container .layui-timeline").empty();

        // 按照时间排序事件（正序）
        eventDetails.sort(function (a, b) {
            return new Date(a.createTime) - new Date(b.createTime);
        });

        // 添加事件到时间轴
        eventDetails.forEach(function (detail) {
            // 设置图标颜色基于事件类型
            var iconColor = "#009688"; // 默认颜色
            if (detail.eventType === "删除") {
                iconColor = "#FF5722"; // 删除操作使用红色
            }

            // 创建时间轴项
            var timelineItem =
                '<li class="layui-timeline-item" style="padding-bottom: 5px;">' +
                '  <i class="layui-icon layui-timeline-axis" style="color: ' + iconColor + '; font-size: 10px;line-height:24px">&#xe63f;</i>' +
                '  <div class="layui-text" style="padding-left: 15px;">' +
                '    <p style="margin: 5px 0px 0px 0px;font-size: 11px;color:#8e939b">' + detail.createTime + '</p>' +
                '    <p style="margin: 0; word-wrap: break-word; word-break: break-all;"><small><span style="color: #3a4e6c;">' + detail.createRole + '</span> ' + detail.createUser + ' ' + detail.eventType + ' ' + detail.operateName + '</small></p>' +
                '    <p style="margin: 5px 0;background-color: #f3f6fa; border-radius: 4px; padding-left: 5px;color:#3a4e6c;font-size: 12px; word-wrap: break-word; word-break: break-all;">' + detail.eventContent + '</p>' +
                '  </div>' +
                '</li>';

            $(".timeline-container .layui-timeline").append(timelineItem);
        });
    }

    //监听窗口关闭事件，当窗口关闭时，主动提交数据及去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。  
    window.onbeforeunload = function () {
        //关闭前更新同步到服务器
        if (isAdd == true || isUpdate == true || isDel == true) {
            if (curGraphic != null) {
                var graLabel = null;
                var curid = curGraphic.attributes["oid"];
                for (var i in zjlayer.graphics.items) {
                    var id = zjlayer.graphics.items[i].attributes["oid"];
                    if (id == curid) {
                        graLabel = zjlayer.graphics.items[i];
                    }
                }
                var tmpjson = {


                    "infoType": "update",
                    "eventInfo": jqJson,
                    "userInfo": [
                        userJson
                    ],
                    "drawInfo": [{ id: curid, featureGraphic: JSON.stringify(curGraphic.toJSON()), labelGraphic: JSON.stringify(graLabel.toJSON()) }],
                };
                if (ws != null && ws.isOnLine == true)
                    ws.send(tmpjson);


                // 新增演练任务操作明细
                var content =
                    '名称【' +
                    curGraphic.attributes['wtmc'] +
                    '】，经纬度[' +
                    graLabel.geometry['x'].toFixed(6) +
                    ',' +
                    graLabel.geometry['y'].toFixed(6) +
                    ']'
                var eventDetailId = jqJson.id.replace('（2D）', '')
                var operateObjName = curGraphic.attributes['operate']
                // 军标图形直接取wtdz, 其它从图片地址抽出操作对象名称
                // var wtdz = curGraphic.attributes['wtdz']
                // var operateObjName = wtdz
                // if (wtdz.indexOf('.png') != -1) {
                //     operateObjName = wtdz
                //         ? wtdz.substring(
                //             wtdz.lastIndexOf('/') + 1,
                //             wtdz.lastIndexOf('.png')
                //         )
                //         : ''
                // }
                var eventDetailJson = {
                    infoType: 'details',
                    eventInfo: jqJson,
                    eventDetails: [
                        {
                            eventId: eventDetailId,
                            operateName: operateObjName,
                            eventContent: content,
                            eventType: '标注',
                            createUser: userJson.createUser,
                            createRole: userJson.createRole,
                            operateId: curGraphic.attributes['id'],
                            createUserId: userJson.id
                        }
                    ]
                }
                if (ws != null && ws.isOnLine == true) ws.send(eventDetailJson)




                isAdd = false;
                isUpdate = false;
                isDel = false;
            }
        }

    }

    /**
     * 用户上线添加标签页图例
     * @param {*} userid 用户标识
     * @param {*} userName 用户姓名
     * @param {*} status 用户在线状态
     * @param {*} colorStr 用户图例颜色
     */
    function setTabLegend(userid, userName, status, colorStr) {
        var fontColor = "#084474";
        if (status != "1") {
            fontColor = "#CFCFCF";
        }

        if ($("#tab_user_" + userid).length == 0) {
            // 不存在则添加新项
            let htmlStr =
                '<li id="tab_user_' + userid + '" class="layui-nav-item">' +
                '<a href="javascript:;" style="display: flex; align-items: flex-start; padding: 5px 0px;">' +
                '<div style="width: 20px; height: 20px; background-color: ' + colorStr + '; border-radius: 3px; flex-shrink: 0; margin-top: 2px; margin-right: 5px;"></div>' +
                '<span style="color:' + fontColor + '; font-size: 14px; word-wrap: break-word; word-break: break-word; white-space: normal; line-height: 1.2;">' + userName + '</span>' +
                '</a>' +
                '</li>';
            $("#onlineUsersList").append(htmlStr);
        } else {
            // 更新状态
            var userElement = $("#tab_user_" + userid + " a");
            userElement.find("div:first").css("background-color", colorStr);
            userElement.find("span").css({
                "color": fontColor,
                "line-height": "1.2"
            }).text(userName);
        }

        // 渲染
        if ($("#onlineUsersList li").length > 0) {
            element.render('nav', 'onlineUsersList');
        }
    }

    /**
     * 用户下线更新标签页图例
     * @param {*} userid 用户标识
     */
    function setTabOutLegend(userid) {
        if ($("#tab_user_" + userid).length != 0) {
            var userElement = $("#tab_user_" + userid + " a");
            userElement.find("span").css("color", "#CFCFCF");
        }
    }

    function setOutLegend(userid) {
        var name = "";
        if ($("#tr_" + userid).length != 0) {
            name = $("#tr_" + userid).find("td:eq(1)").text().replace("(离线)", "");
            $("#tr_" + userid).find("td:eq(1)").text(name + "(离线)");
            $("#tr_" + userid).find("td:eq(1)").css("color", "#CFCFCF");

            // 更新在线人员标签页的状态
            setTabOutLegend(userid);
        }
        return name;
    }

    /**
     * 清空在线人员标签页
     */
    function clearTabLegend() {
        $("#onlineUsersList").empty();
    }
</script>

</html>